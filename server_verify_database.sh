#!/bin/bash

# 服务器数据库验证脚本
# 在Ubuntu服务器上运行此脚本来验证Docker容器的数据库配置

echo "🔍 验证服务器上的容器数据库配置"
echo "=================================================="

# 检查容器状态
echo "=== 检查容器状态 ==="
docker compose ps
echo ""

# 检查容器是否运行
if ! docker compose ps | grep -q "Up"; then
    echo "❌ 容器未运行，请先启动服务"
    echo "运行: docker compose up -d"
    exit 1
fi

# 检查宿主机数据库文件
echo "=== 检查宿主机数据库文件 ==="
if [ -f "./social_media.db" ]; then
    HOST_SIZE=$(stat -c%s "./social_media.db")
    HOST_MTIME=$(stat -c%y "./social_media.db")
    echo "✅ 宿主机数据库文件存在"
    echo "   大小: $HOST_SIZE bytes"
    echo "   修改时间: $HOST_MTIME"
else
    echo "❌ 宿主机数据库文件不存在"
fi
echo ""

# 检查容器内数据库文件
echo "=== 检查容器内数据库文件 ==="
if docker exec social_media_backend test -f /app/social_media.db; then
    CONTAINER_SIZE=$(docker exec social_media_backend stat -c%s /app/social_media.db)
    CONTAINER_MTIME=$(docker exec social_media_backend stat -c%y /app/social_media.db)
    echo "✅ 容器内数据库文件存在"
    echo "   大小: $CONTAINER_SIZE bytes"
    echo "   修改时间: $CONTAINER_MTIME"
    
    # 比较文件大小
    if [ "$HOST_SIZE" = "$CONTAINER_SIZE" ]; then
        echo "✅ 文件大小匹配，挂载正确"
    else
        echo "❌ 文件大小不匹配，挂载可能有问题"
        echo "   宿主机: $HOST_SIZE bytes"
        echo "   容器内: $CONTAINER_SIZE bytes"
    fi
else
    echo "❌ 容器内数据库文件不存在"
fi
echo ""

# 检查容器内数据库内容
echo "=== 检查容器内数据库内容 ==="
USER_COUNT=$(docker exec social_media_backend sqlite3 /app/social_media.db "SELECT COUNT(*) FROM users;" 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "✅ 数据库可访问"
    echo "   用户数量: $USER_COUNT"
    
    echo "   用户列表:"
    docker exec social_media_backend sqlite3 /app/social_media.db "SELECT '   ID: ' || id || ', 用户名: ' || username || ', 邮箱: ' || email FROM users LIMIT 5;" 2>/dev/null
else
    echo "❌ 无法访问数据库或sqlite3未安装"
fi
echo ""

# 检查环境变量
echo "=== 检查容器环境变量 ==="
echo "DATABASE_URL:"
docker exec social_media_backend printenv DATABASE_URL 2>/dev/null || echo "   未设置"
echo "SECRET_KEY:"
SECRET_KEY=$(docker exec social_media_backend printenv SECRET_KEY 2>/dev/null)
if [ -n "$SECRET_KEY" ]; then
    echo "   已设置 (长度: ${#SECRET_KEY})"
else
    echo "   未设置"
fi
echo ""

# 测试API连接
echo "=== 测试API连接 ==="
if curl -s -f http://localhost:8000/ > /dev/null; then
    echo "✅ 后端API可访问"
    
    # 测试登录API
    echo "测试登录API..."
    
    # 已知用户列表（从数据库验证脚本获得）
    declare -a users=("testuser:testpassword123" "admin:admin123" "admin:password" "admin:admin")
    
    for user_pass in "${users[@]}"; do
        IFS=':' read -r username password <<< "$user_pass"
        echo "  测试用户: $username"
        
        response=$(curl -s -w "%{http_code}" -X POST \
            -H "Content-Type: application/json" \
            -d "{\"username\":\"$username\",\"password\":\"$password\"}" \
            http://localhost:8000/api/auth/login)
        
        http_code="${response: -3}"
        response_body="${response%???}"
        
        if [ "$http_code" = "200" ]; then
            echo "    ✅ 登录成功 (200)"
            echo "    响应: $response_body" | head -c 100
            echo "..."
            break
        elif [ "$http_code" = "401" ]; then
            echo "    ❌ 认证失败 (401)"
            echo "    错误: $response_body"
        else
            echo "    ❌ 其他错误 ($http_code)"
            echo "    响应: $response_body"
        fi
    done
else
    echo "❌ 后端API不可访问"
    echo "检查容器日志:"
    docker compose logs --tail=10 backend
fi
echo ""

# 检查前端访问
echo "=== 检查前端访问 ==="
if curl -s -f http://localhost:3000/ > /dev/null; then
    echo "✅ 前端可访问"
else
    echo "❌ 前端不可访问"
fi
echo ""

# 检查Docker挂载
echo "=== 检查Docker挂载配置 ==="
echo "当前docker-compose.yml中的挂载配置:"
grep -A 10 "volumes:" docker-compose.yml | grep -E "(volumes:|social_media\.db)"
echo ""

# 显示容器详细信息
echo "=== 容器详细信息 ==="
docker inspect social_media_backend | grep -A 20 '"Mounts"' | head -30
echo ""

echo "=================================================="
echo "验证完成！"
echo ""
echo "💡 如果发现问题："
echo "1. 检查docker-compose.yml中的数据库挂载配置"
echo "2. 重新启动服务: docker compose restart"
echo "3. 查看详细日志: docker compose logs backend"
echo "4. 检查文件权限: ls -la social_media.db"
