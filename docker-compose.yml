version: '3.8'

services:
  # 后端API服务
  backend:
    build: .
    container_name: social_media_backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=sqlite:///./social_media.db
      - SECRET_KEY=${SECRET_KEY}
      - ALGORITHM=${ALGORITHM}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES}
    volumes:
      - ./user_data:/app/user_data
      - ./data:/app/data
      - ./social_media.db:/app/social_media.db
    ports:
      - "8000:8000"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: social_media_frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - app-network

# volumes:
  # app_data:  # 不需要，使用本地目录挂载

networks:
  app-network:
    driver: bridge