# 社交媒体管理系统 - 局域网部署方案

## 📋 部署概述

本方案专门针对Windows 11主机的局域网部署，通过内网穿透技术实现公网访问。系统采用Docker容器化部署，包含nginx反向代理、后端API服务、前端应用等组件。

### 🎯 部署目标
- **主机地址**: ************ (Windows 11 + OpenSSH Server)
- **公网域名**: sm.dev.mynatapp.cc (通过内网穿透)
- **架构**: Docker + Nginx + FastAPI + React

## 🏗️ 系统架构

```
Internet → 内网穿透 → Windows 11主机(************) → Docker容器
                                                    ├── Nginx (反向代理)
                                                    ├── Backend (FastAPI)
                                                    ├── Frontend (React)
                                                    └── Redis (缓存)
```

## 📦 部署文件说明

### 核心配置文件
- `docker-compose.lan.yml` - 局域网专用Docker编排配置
- `nginx/nginx.conf` - Nginx主配置文件
- `nginx/conf.d/default.conf` - 站点配置文件
- `.env.lan` - 环境变量配置模板
- `deploy-lan.sh` - 自动化部署脚本

### 目录结构
```
social-media-manager/
├── docker-compose.lan.yml      # 局域网Docker配置
├── deploy-lan.sh               # 部署脚本
├── .env.lan                    # 环境变量模板
├── nginx/                      # Nginx配置
│   ├── nginx.conf             # 主配置
│   ├── conf.d/
│   │   └── default.conf       # 站点配置
│   ├── ssl/                   # SSL证书目录
│   └── logs/                  # 日志目录
├── data/                      # 应用数据
├── user_data/                 # 用户数据
└── logs/                      # 应用日志
```

## 🚀 部署步骤

### 1. 准备Windows 11主机

#### 1.1 安装Docker Desktop
```powershell
# 下载并安装Docker Desktop for Windows
# https://www.docker.com/products/docker-desktop/

# 启动Docker Desktop并确保WSL2后端已启用
```

#### 1.2 启用OpenSSH Server
```powershell
# 以管理员身份运行PowerShell
Add-WindowsCapability -Online -Name OpenSSH.Server~~~~0.0.1.0
Start-Service sshd
Set-Service -Name sshd -StartupType 'Automatic'
```

#### 1.3 配置防火墙
```powershell
# 允许SSH连接
New-NetFirewallRule -Name sshd -DisplayName 'OpenSSH Server (sshd)' -Enabled True -Direction Inbound -Protocol TCP -Action Allow -LocalPort 22

# 允许HTTP/HTTPS访问
New-NetFirewallRule -Name "HTTP" -DisplayName "HTTP" -Enabled True -Direction Inbound -Protocol TCP -Action Allow -LocalPort 80
New-NetFirewallRule -Name "HTTPS" -DisplayName "HTTPS" -Enabled True -Direction Inbound -Protocol TCP -Action Allow -LocalPort 443
```

### 2. 配置内网穿透

#### 2.1 使用natapp配置
```bash
# 在natapp控制台配置隧道
# 协议: http
# 本地端口: 80
# 自定义域名: sm.dev.mynatapp.cc
# 本地地址: ************:80
```

#### 2.2 启动内网穿透客户端
```powershell
# 下载natapp客户端
# 运行: natapp -authtoken=your_token
```

### 3. 部署应用

#### 3.1 通过SSH连接到主机
```bash
ssh username@************
```

#### 3.2 克隆项目代码
```bash
git clone https://github.com/leaf3000/social-media-manager.git
cd social-media-manager
```

#### 3.3 配置环境变量
```bash
# 复制环境变量模板
cp .env.lan .env

# 编辑配置文件
nano .env
```

**重要配置项**:
```bash
# 域名配置
DOMAIN=sm.dev.mynatapp.cc
LOCAL_IP=************

# 安全密钥（自动生成）
SECRET_KEY=your-super-secret-key-change-this-in-production

# 飞书配置
# FEISHU_APP_ID=your_feishu_app_id
# FEISHU_APP_SECRET=your_feishu_app_secret

# 允许的访问源
ALLOWED_ORIGINS=http://sm.dev.mynatapp.cc,http://************,http://localhost:3000
```

#### 3.4 执行部署
```bash
# 运行部署脚本
./deploy-lan.sh
```

## 🔧 服务管理

### 常用命令
```bash
# 查看服务状态
docker-compose -f docker-compose.lan.yml ps

# 查看日志
docker-compose -f docker-compose.lan.yml logs -f

# 重启服务
docker-compose -f docker-compose.lan.yml restart

# 停止服务
docker-compose -f docker-compose.lan.yml down

# 更新服务
git pull
docker-compose -f docker-compose.lan.yml up --build -d
```

### 健康检查
```bash
# 检查服务健康状态
curl http://************/health
curl http://sm.dev.mynatapp.cc/health

# 检查API服务
curl http://************/api/
curl http://sm.dev.mynatapp.cc/api/
```

## 🔒 安全配置

### 1. 网络安全
- 配置Windows防火墙规则
- 限制SSH访问IP范围
- 使用强密码和密钥认证

### 2. 应用安全
- 定期更新Docker镜像
- 配置nginx访问限制
- 启用请求频率限制

### 3. 数据安全
- 定期备份数据库和用户数据
- 配置日志轮转
- 监控异常访问

## 📊 监控和维护

### 1. 日志监控
```bash
# 查看nginx访问日志
tail -f nginx/logs/access.log

# 查看应用日志
tail -f logs/app.log

# 查看容器日志
docker-compose -f docker-compose.lan.yml logs -f [service_name]
```

### 2. 性能监控
```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
df -h
```

### 3. 备份策略
```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf backup_${DATE}.tar.gz data/ user_data/ .env
```

## 🚨 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 检查Docker服务
systemctl status docker

# 检查端口占用
netstat -tlnp | grep :80

# 查看详细错误
docker-compose -f docker-compose.lan.yml logs
```

#### 2. 内网穿透连接失败
- 检查natapp客户端状态
- 验证域名解析
- 确认本地服务可访问

#### 3. 数据库连接问题
```bash
# 检查数据库文件权限
ls -la data/

# 重置数据库
rm data/social_media.db
docker-compose -f docker-compose.lan.yml restart backend
```

## 📞 技术支持

### 联系方式
- GitHub Issues: https://github.com/leaf3000/social-media-manager/issues
- 邮箱支持: <EMAIL>

### 文档资源
- API文档: http://sm.dev.mynatapp.cc/docs
- 用户手册: ./docs/
- 开发指南: ./docs/development.md

---

**注意**: 本部署方案适用于开发和测试环境，生产环境请额外配置SSL证书、数据库集群、负载均衡等高可用组件。
