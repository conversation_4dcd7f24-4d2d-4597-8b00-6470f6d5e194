#!/bin/bash

# 服务器部署脚本
# 在服务器上运行此脚本进行部署

set -e

echo "🚀 开始在服务器上部署社交媒体管理系统..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查docker compose是否安装
if ! command -v docker compose &> /dev/null; then
    echo "❌ docker compose未安装，请先安装docker compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p data
mkdir -p user_data
mkdir -p logs
mkdir -p temp_downloads

# 设置权限
chmod 755 data user_data logs temp_downloads

# 检查环境变量文件
if [ ! -f .env.production ]; then
    echo "❌ .env.production文件不存在，请先创建并配置环境变量"
    exit 1
fi

# 复制环境变量文件
cp .env.production .env

# 停止现有容器
echo "🛑 停止现有容器..."
docker compose down || true

# 清理悬空镜像（安全清理）
echo "🧹 清理悬空镜像..."
docker image prune -f || true

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "📊 检查服务状态..."
docker compose ps

# 检查后端健康状态
echo "🏥 检查后端健康状态..."
if curl -f http://localhost:8000/ > /dev/null 2>&1; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    docker compose logs backend
    exit 1
fi

# 检查前端服务
echo "🌐 检查前端服务..."
if curl -f http://localhost:3000/ > /dev/null 2>&1; then
    echo "✅ 前端服务启动成功"
else
    echo "❌ 前端服务启动失败"
    docker compose logs frontend
    exit 1
fi

echo "🎉 服务器部署完成！"
echo "📱 前端内部访问地址: http://localhost:3000"
echo "🔧 后端内部访问地址: http://localhost:8000"
echo ""
echo "📊 查看日志: docker compose logs -f"
echo "🛑 停止服务: docker compose down"
echo ""
echo "⚠️  请确保已配置Nginx反向代理："
echo "   - sm.dev.mynatapp.cc -> localhost:3000"
echo "   - sm.dev.mynatapp.cc/api -> localhost:8000"
