# 社交媒体管理系统 - 局域网部署快速指南

## 🚀 快速开始

### Windows 11 主机部署

#### 1. 准备环境
```powershell
# 以管理员身份运行PowerShell
# 安装Docker Desktop for Windows
# 启用WSL2后端
```

#### 2. 克隆项目
```bash
git clone https://github.com/leaf3000/social-media-manager.git
cd social-media-manager
```

#### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.lan .env

# 编辑配置文件，设置飞书应用信息
nano .env
```

#### 4. 一键部署
```bash
# Linux/macOS
./deploy-lan.sh

# Windows PowerShell
.\deploy-windows.ps1
```

## 📋 访问地址

- **内网穿透**: http://sm.dev.mynatapp.cc
- **本地访问**: http://************
- **本地访问**: http://localhost

## 🔧 管理命令

### 基础操作
```bash
# 查看服务状态
docker-compose -f docker-compose.lan.yml ps

# 启动服务
./scripts/manage.sh start

# 停止服务
./scripts/manage.sh stop

# 重启服务
./scripts/manage.sh restart

# 查看日志
./scripts/manage.sh logs
```

### 高级操作
```bash
# 监控系统
./scripts/monitor.sh all

# 备份数据
./scripts/backup.sh full

# 进入容器
./scripts/manage.sh enter backend

# 更新代码
./scripts/manage.sh update
```

## 📁 目录结构

```
social-media-manager/
├── docker-compose.lan.yml      # 局域网Docker配置
├── deploy-lan.sh               # Linux/macOS部署脚本
├── deploy-windows.ps1          # Windows部署脚本
├── .env.lan                    # 环境变量模板
├── nginx/                      # Nginx配置
│   ├── nginx.conf             # 主配置
│   └── conf.d/default.conf    # 站点配置
├── scripts/                   # 管理脚本
│   ├── manage.sh              # 服务管理
│   ├── monitor.sh             # 系统监控
│   └── backup.sh              # 数据备份
├── data/                      # 应用数据
├── user_data/                 # 用户数据
├── logs/                      # 应用日志
└── backups/                   # 备份文件
```

## 🔒 安全配置

### 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Windows (管理员PowerShell)
New-NetFirewallRule -DisplayName "HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
New-NetFirewallRule -DisplayName "HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow
```

### 环境变量配置
```bash
# 必须修改的配置项
SECRET_KEY=your-generated-secret-key
FEISHU_APP_ID=your_feishu_app_id
FEISHU_APP_SECRET=your_feishu_app_secret

# 网络配置
DOMAIN=sm.dev.mynatapp.cc
LOCAL_IP=************
ALLOWED_ORIGINS=http://sm.dev.mynatapp.cc,http://************
```

## 🌐 内网穿透配置

### natapp配置步骤
1. 注册natapp账号
2. 创建隧道配置：
   - 协议: http
   - 本地端口: 80
   - 自定义域名: sm.dev.mynatapp.cc
3. 下载客户端并启动

### 启动内网穿透
```bash
# Linux/macOS
./natapp -authtoken=your_token

# Windows
natapp.exe -authtoken=your_token
```

## 🛠️ 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 检查Docker状态
docker info

# 查看详细日志
docker-compose -f docker-compose.lan.yml logs

# 重新构建
./scripts/manage.sh rebuild
```

#### 2. 端口被占用
```bash
# 检查端口占用
netstat -tlnp | grep :80

# 停止冲突服务
sudo systemctl stop apache2
sudo systemctl stop nginx
```

#### 3. 内网穿透连接失败
- 检查natapp客户端状态
- 验证域名解析
- 确认本地服务可访问

#### 4. 权限问题
```bash
# 修复文件权限
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh
chmod +x deploy-lan.sh
```

## 📊 监控和维护

### 健康检查
```bash
# 检查服务健康
curl http://localhost/health
curl http://sm.dev.mynatapp.cc/health

# 系统监控
./scripts/monitor.sh all

# 实时监控
./scripts/monitor.sh realtime
```

### 数据备份
```bash
# 完整备份
./scripts/backup.sh full

# 分类备份
./scripts/backup.sh database
./scripts/backup.sh user_data
./scripts/backup.sh config
```

### 日志管理
```bash
# 查看访问日志
tail -f nginx/logs/access.log

# 查看错误日志
tail -f nginx/logs/error.log

# 查看应用日志
./scripts/manage.sh logs backend
```

## 📞 技术支持

### 文档资源
- [完整部署文档](./DEPLOYMENT_LAN.md)
- [API文档](http://sm.dev.mynatapp.cc/docs)
- [GitHub Issues](https://github.com/leaf3000/social-media-manager/issues)

### 快速命令参考
```bash
# 部署相关
./deploy-lan.sh                 # 完整部署
./scripts/manage.sh start       # 启动服务
./scripts/manage.sh stop        # 停止服务
./scripts/manage.sh status      # 查看状态

# 监控相关
./scripts/monitor.sh health     # 健康检查
./scripts/monitor.sh resources  # 资源使用
./scripts/monitor.sh logs       # 错误日志

# 维护相关
./scripts/backup.sh full        # 完整备份
./scripts/manage.sh update      # 更新代码
./scripts/manage.sh cleanup     # 清理资源
```

---

**注意**: 本配置适用于开发和测试环境，生产环境请参考完整的部署文档进行安全加固。
