#!/usr/bin/env python3
"""
测试数据下载修复的脚本
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_data_types_api():
    """测试数据类型API，确认user_source已被移除"""
    
    print("=== 测试数据类型配置 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/data-download/data-types")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            data_types = data.get("data_types", {})
            
            print("可用的数据类型:")
            for key, value in data_types.items():
                print(f"  {key}: {value}")
            
            # 检查是否还包含user_source
            if "user_source" in data_types:
                print("❌ 错误：仍然包含user_source")
            else:
                print("✅ 正确：已移除user_source")
                
            # 检查是否包含预期的类型
            expected_types = ["content_trend", "content_source", "content_detail", "user_channel"]
            missing_types = [t for t in expected_types if t not in data_types]
            
            if missing_types:
                print(f"❌ 缺少数据类型: {missing_types}")
            else:
                print("✅ 所有预期的数据类型都存在")
                
        else:
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_data_types_api()
