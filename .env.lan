# 社交媒体管理系统 - 局域网部署环境变量配置

# ===========================================
# 应用基础配置
# ===========================================
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# 数据库配置
# ===========================================
# SQLite配置（默认）
DATABASE_URL=sqlite:///./social_media.db

# MySQL配置（可选，取消注释启用）
# DATABASE_URL=mysql+pymysql://app_user:app123@mysql:3306/social_media_manager
# MYSQL_ROOT_PASSWORD=root123
# MYSQL_PASSWORD=app123

# ===========================================
# Redis配置
# ===========================================
# REDIS_URL=redis://:redis123@redis:6379/0
# REDIS_PASSWORD=redis123

# ===========================================
# 飞书应用配置
# ===========================================
# FEISHU_APP_ID=your_feishu_app_id
# FEISHU_APP_SECRET=your_feishu_app_secret

# ===========================================
# 网络配置
# ===========================================
# 内网穿透域名
DOMAIN=sm.dev.mynatapp.cc
# 本地IP地址
LOCAL_IP=************
# 允许的源（CORS）
ALLOWED_ORIGINS=http://sm.dev.mynatapp.cc,http://************,http://localhost:3000

# ===========================================
# 安全配置
# ===========================================
# 是否启用HTTPS重定向
FORCE_HTTPS=false
# 是否启用安全头
SECURITY_HEADERS=true

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log

# ===========================================
# 文件上传配置
# ===========================================
MAX_FILE_SIZE=100MB
UPLOAD_PATH=/app/user_data

# ===========================================
# 时区配置
# ===========================================
TZ=Asia/Shanghai
