#!/bin/bash

# 社交媒体管理系统 - 局域网部署脚本
# 适用于Windows 11主机通过内网穿透部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Docker服务是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p nginx/conf.d
    mkdir -p nginx/ssl
    mkdir -p nginx/logs
    mkdir -p data
    mkdir -p user_data
    mkdir -p logs
    
    log_success "目录创建完成"
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    if [ ! -f .env.lan ]; then
        log_error ".env.lan文件不存在，请先创建环境变量文件"
        exit 1
    fi
    
    # 复制环境变量文件
    cp .env.lan .env
    
    # 生成随机密钥（如果需要）
    if grep -q "your-super-secret-key-change-this-in-production" .env; then
        log_warning "检测到默认密钥，正在生成新的安全密钥..."
        
        # 生成新的SECRET_KEY
        NEW_SECRET_KEY=$(openssl rand -base64 64 | tr -d '\n')
        sed -i "s/your-super-secret-key-change-this-in-production/$NEW_SECRET_KEY/" .env
        
        log_success "新的安全密钥已生成"
    fi
    
    log_success "环境变量设置完成"
}

# 停止现有服务
stop_existing_services() {
    log_info "停止现有服务..."
    
    # 停止可能冲突的服务
    if docker-compose -f docker-compose.lan.yml ps -q | grep -q .; then
        docker-compose -f docker-compose.lan.yml down
        log_success "现有服务已停止"
    else
        log_info "没有运行中的服务"
    fi
}

# 构建和启动服务
build_and_start() {
    log_info "构建和启动服务..."
    
    # 拉取最新镜像
    docker-compose -f docker-compose.lan.yml pull
    
    # 构建自定义镜像
    docker-compose -f docker-compose.lan.yml build --no-cache
    
    # 启动服务
    docker-compose -f docker-compose.lan.yml up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待后端服务
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/api/ &> /dev/null; then
            log_success "后端服务已就绪"
            break
        fi
        
        log_info "等待后端服务启动... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "后端服务启动超时"
        exit 1
    fi
    
    # 等待前端服务
    attempt=1
    while [ $attempt -le 10 ]; do
        if curl -f http://localhost/ &> /dev/null; then
            log_success "前端服务已就绪"
            break
        fi

        log_info "等待前端服务启动... ($attempt/10)"
        sleep 3
        ((attempt++))
    done

    if [ $attempt -gt 10 ]; then
        log_warning "前端服务可能未完全就绪，请稍后检查"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "=========================================="
    echo "  社交媒体管理系统 - 局域网部署信息"
    echo "=========================================="
    echo
    echo "🌐 访问地址："
    echo "  - 内网穿透: http://sm.dev.mynatapp.cc"
    echo "  - 本地访问: http://************"
    echo "  - 本地访问: http://localhost"
    echo
    echo "🔧 管理命令："
    echo "  查看状态: docker-compose -f docker-compose.lan.yml ps"
    echo "  查看日志: docker-compose -f docker-compose.lan.yml logs -f"
    echo "  重启服务: docker-compose -f docker-compose.lan.yml restart"
    echo "  停止服务: docker-compose -f docker-compose.lan.yml down"
    echo
    echo "📁 重要目录："
    echo "  用户数据: ./user_data"
    echo "  应用数据: ./data"
    echo "  日志文件: ./logs"
    echo "  Nginx日志: ./nginx/logs"
    echo
    echo "🔒 安全提醒："
    echo "  - 请确保防火墙已正确配置"
    echo "  - 建议定期备份数据"
    echo "  - 生产环境请启用HTTPS"
    echo
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "  社交媒体管理系统 - 局域网部署脚本"
    echo "=========================================="
    echo
    
    check_docker
    create_directories
    setup_environment
    stop_existing_services
    build_and_start
    wait_for_services
    show_deployment_info
}

# 执行主函数
main "$@"
