#!/bin/bash

# 本地上传脚本
# 将项目文件上传到服务器

# 配置服务器信息
SERVER_USER=""  # 修改为你的服务器用户名
SERVER_HOST=""  # 修改为你的服务器IP
SERVER_PATH=""  # 服务器上的部署路径

echo "🚀 开始上传项目到服务器..."

# 检查rsync是否安装
if ! command -v rsync &> /dev/null; then
    echo "❌ rsync未安装，请先安装rsync"
    exit 1
fi

# 创建排除文件列表
cat > .rsync-exclude << EOF
.git/
.gitignore
node_modules/
venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.DS_Store
.vscode/
.idea/
*.swp
*.swo
*~
.env
.env.local
.env.development
.env.test
social_media_manager.db
user_data/
data/
logs/
frontend/build/
frontend/node_modules/
test/
EOF

echo "📦 准备上传文件..."

# 在服务器上创建目录
ssh ${SERVER_USER}@${SERVER_HOST} "mkdir -p ${SERVER_PATH}"

# 使用rsync上传文件（安全模式，不删除目标目录的其他文件）
echo "📤 上传项目文件..."
rsync -avz --progress \
    --exclude-from=.rsync-exclude \
    ./ ${SERVER_USER}@${SERVER_HOST}:${SERVER_PATH}/

# 上传环境配置文件
echo "📤 上传环境配置文件..."
scp .env.production ${SERVER_USER}@${SERVER_HOST}:${SERVER_PATH}/

# 设置执行权限
echo "🔧 设置执行权限..."
ssh ${SERVER_USER}@${SERVER_HOST} "chmod +x ${SERVER_PATH}/server-deploy.sh"

# 清理临时文件
rm -f .rsync-exclude

echo "✅ 文件上传完成！"
echo ""
echo "接下来请在服务器上执行以下命令："
echo "1. ssh ${SERVER_USER}@${SERVER_HOST}"
echo "2. cd ${SERVER_PATH}"
echo "3. 编辑 .env.production 文件，配置相关参数"
echo "4. ./server-deploy.sh"
echo ""
echo "📋 还需要配置："
echo "- 配置Docker镜像源（如果还没配置）"
echo "- 配置Nginx反向代理"
echo "- 配置SSL证书"
