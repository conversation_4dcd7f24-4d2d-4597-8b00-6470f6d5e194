#!/bin/bash

# 社交媒体管理系统 - 监控脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_services() {
    log_info "检查Docker服务状态..."
    
    echo "=========================================="
    echo "  Docker容器状态"
    echo "=========================================="
    docker-compose -f docker-compose.lan.yml ps
    echo
}

# 检查健康状态
check_health() {
    log_info "检查应用健康状态..."
    
    echo "=========================================="
    echo "  健康检查"
    echo "=========================================="
    
    # 检查nginx健康状态
    if curl -f -s http://localhost/health > /dev/null; then
        log_success "Nginx服务正常"
    else
        log_error "Nginx服务异常"
    fi
    
    # 检查后端API
    if curl -f -s http://localhost/api/ > /dev/null; then
        log_success "后端API服务正常"
    else
        log_error "后端API服务异常"
    fi
    
    # 检查前端服务
    if curl -f -s http://localhost/ > /dev/null; then
        log_success "前端服务正常"
    else
        log_error "前端服务异常"
    fi
    
    echo
}

# 检查资源使用
check_resources() {
    log_info "检查系统资源使用..."
    
    echo "=========================================="
    echo "  系统资源使用"
    echo "=========================================="
    
    # 内存使用
    echo "内存使用:"
    free -h
    echo
    
    # 磁盘使用
    echo "磁盘使用:"
    df -h
    echo
    
    # Docker容器资源使用
    echo "容器资源使用:"
    docker stats --no-stream
    echo
}

# 检查日志错误
check_logs() {
    log_info "检查最近的错误日志..."
    
    echo "=========================================="
    echo "  最近的错误日志"
    echo "=========================================="
    
    # 检查nginx错误日志
    if [ -f "nginx/logs/error.log" ]; then
        echo "Nginx错误日志 (最近10行):"
        tail -n 10 nginx/logs/error.log 2>/dev/null || echo "无错误日志"
        echo
    fi
    
    # 检查应用日志
    echo "应用容器日志 (最近20行错误):"
    docker-compose -f docker-compose.lan.yml logs --tail=20 backend 2>/dev/null | grep -i error || echo "无错误日志"
    echo
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    
    echo "=========================================="
    echo "  网络连接检查"
    echo "=========================================="
    
    # 检查内网穿透域名
    if curl -f -s --connect-timeout 5 http://sm.dev.mynatapp.cc/health > /dev/null; then
        log_success "内网穿透域名访问正常"
    else
        log_warning "内网穿透域名访问异常"
    fi
    
    # 检查本地IP访问
    if curl -f -s --connect-timeout 5 http://************/health > /dev/null; then
        log_success "本地IP访问正常"
    else
        log_warning "本地IP访问异常"
    fi
    
    echo
}

# 生成监控报告
generate_report() {
    local report_file="monitor_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log_info "生成监控报告..."
    
    {
        echo "=========================================="
        echo "  社交媒体管理系统监控报告"
        echo "  生成时间: $(date)"
        echo "=========================================="
        echo
        
        echo "1. Docker服务状态:"
        docker-compose -f docker-compose.lan.yml ps
        echo
        
        echo "2. 系统资源使用:"
        free -h
        echo
        df -h
        echo
        
        echo "3. 容器资源使用:"
        docker stats --no-stream
        echo
        
        echo "4. 最近错误日志:"
        docker-compose -f docker-compose.lan.yml logs --tail=50 2>/dev/null | grep -i error || echo "无错误日志"
        echo
        
    } > "$report_file"
    
    log_success "监控报告已生成: $report_file"
}

# 实时监控
real_time_monitor() {
    log_info "启动实时监控 (按Ctrl+C退出)..."
    
    while true; do
        clear
        echo "=========================================="
        echo "  实时监控 - $(date)"
        echo "=========================================="
        
        # 服务状态
        echo "服务状态:"
        docker-compose -f docker-compose.lan.yml ps
        echo
        
        # 资源使用
        echo "资源使用:"
        docker stats --no-stream
        echo
        
        # 最近访问
        echo "最近访问 (最后5条):"
        tail -n 5 nginx/logs/access.log 2>/dev/null || echo "无访问日志"
        echo
        
        sleep 10
    done
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  services    检查服务状态"
    echo "  health      检查健康状态"
    echo "  resources   检查资源使用"
    echo "  logs        检查错误日志"
    echo "  network     检查网络连接"
    echo "  report      生成监控报告"
    echo "  realtime    实时监控"
    echo "  all         执行所有检查"
    echo "  help        显示帮助信息"
    echo
}

# 主函数
main() {
    case "${1:-all}" in
        "services")
            check_services
            ;;
        "health")
            check_health
            ;;
        "resources")
            check_resources
            ;;
        "logs")
            check_logs
            ;;
        "network")
            check_network
            ;;
        "report")
            generate_report
            ;;
        "realtime")
            real_time_monitor
            ;;
        "all")
            check_services
            check_health
            check_resources
            check_logs
            check_network
            ;;
        "help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
