#!/bin/bash

# 社交媒体管理系统 - 数据备份脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="social_media_backup_${DATE}"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录..."
    mkdir -p "$BACKUP_DIR"
    log_success "备份目录创建完成: $BACKUP_DIR"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    if [ -f "data/social_media.db" ]; then
        cp data/social_media.db "$BACKUP_DIR/${BACKUP_NAME}_database.db"
        log_success "数据库备份完成"
    else
        log_error "数据库文件不存在"
    fi
}

# 备份用户数据
backup_user_data() {
    log_info "备份用户数据..."
    
    if [ -d "user_data" ]; then
        tar -czf "$BACKUP_DIR/${BACKUP_NAME}_user_data.tar.gz" user_data/
        log_success "用户数据备份完成"
    else
        log_error "用户数据目录不存在"
    fi
}

# 备份配置文件
backup_config() {
    log_info "备份配置文件..."
    
    tar -czf "$BACKUP_DIR/${BACKUP_NAME}_config.tar.gz" \
        .env \
        nginx/ \
        docker-compose.lan.yml \
        2>/dev/null || true
    
    log_success "配置文件备份完成"
}

# 备份日志文件
backup_logs() {
    log_info "备份日志文件..."
    
    if [ -d "logs" ] || [ -d "nginx/logs" ]; then
        tar -czf "$BACKUP_DIR/${BACKUP_NAME}_logs.tar.gz" \
            logs/ \
            nginx/logs/ \
            2>/dev/null || true
        log_success "日志文件备份完成"
    else
        log_info "没有日志文件需要备份"
    fi
}

# 创建完整备份
create_full_backup() {
    log_info "创建完整备份..."
    
    tar -czf "$BACKUP_DIR/${BACKUP_NAME}_full.tar.gz" \
        data/ \
        user_data/ \
        .env \
        nginx/ \
        docker-compose.lan.yml \
        logs/ \
        2>/dev/null || true
    
    log_success "完整备份创建完成: ${BACKUP_NAME}_full.tar.gz"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份文件..."
    
    # 保留最近7天的备份
    find "$BACKUP_DIR" -name "social_media_backup_*" -mtime +7 -delete 2>/dev/null || true
    
    log_success "旧备份文件清理完成"
}

# 显示备份信息
show_backup_info() {
    log_success "备份完成！"
    echo
    echo "=========================================="
    echo "  备份信息"
    echo "=========================================="
    echo "备份时间: $(date)"
    echo "备份目录: $BACKUP_DIR"
    echo "备份文件:"
    ls -lh "$BACKUP_DIR"/*${DATE}* 2>/dev/null || echo "  无备份文件"
    echo
    echo "恢复命令示例:"
    echo "  tar -xzf $BACKUP_DIR/${BACKUP_NAME}_full.tar.gz"
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "  社交媒体管理系统 - 数据备份"
    echo "=========================================="
    echo
    
    create_backup_dir
    
    case "${1:-full}" in
        "database")
            backup_database
            ;;
        "user_data")
            backup_user_data
            ;;
        "config")
            backup_config
            ;;
        "logs")
            backup_logs
            ;;
        "full")
            create_full_backup
            ;;
        *)
            log_error "未知的备份类型: $1"
            echo "用法: $0 [database|user_data|config|logs|full]"
            exit 1
            ;;
    esac
    
    cleanup_old_backups
    show_backup_info
}

# 执行主函数
main "$@"
