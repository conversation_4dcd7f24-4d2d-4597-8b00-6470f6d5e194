#!/bin/bash

# 社交媒体管理系统 - 管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

COMPOSE_FILE="docker-compose.lan.yml"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    docker-compose -f "$COMPOSE_FILE" up -d
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose -f "$COMPOSE_FILE" down
    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    docker-compose -f "$COMPOSE_FILE" restart
    log_success "服务重启完成"
}

# 重新构建服务
rebuild_services() {
    log_info "重新构建服务..."
    docker-compose -f "$COMPOSE_FILE" down
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    docker-compose -f "$COMPOSE_FILE" up -d
    log_success "服务重新构建完成"
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    docker-compose -f "$COMPOSE_FILE" ps
}

# 查看日志
show_logs() {
    local service="${1:-}"
    
    if [ -n "$service" ]; then
        log_info "查看 $service 服务日志..."
        docker-compose -f "$COMPOSE_FILE" logs -f "$service"
    else
        log_info "查看所有服务日志..."
        docker-compose -f "$COMPOSE_FILE" logs -f
    fi
}

# 进入容器
enter_container() {
    local service="${1:-backend}"
    
    log_info "进入 $service 容器..."
    docker-compose -f "$COMPOSE_FILE" exec "$service" /bin/bash
}

# 更新代码
update_code() {
    log_info "更新代码..."
    
    # 备份当前配置
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    
    # 拉取最新代码
    git pull
    
    # 重新构建和启动
    docker-compose -f "$COMPOSE_FILE" up --build -d
    
    log_success "代码更新完成"
}

# 清理资源
cleanup() {
    log_warning "清理Docker资源..."
    
    # 停止服务
    docker-compose -f "$COMPOSE_FILE" down
    
    # 清理未使用的镜像和容器
    docker system prune -f
    
    log_success "资源清理完成"
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    if [ -f "scripts/backup.sh" ]; then
        ./scripts/backup.sh
    else
        log_error "备份脚本不存在"
    fi
}

# 恢复数据
restore_data() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件"
        echo "用法: $0 restore <backup_file>"
        return 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    log_warning "恢复数据将覆盖现有数据，是否继续? (y/N)"
    read -r confirm
    
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        log_info "停止服务..."
        docker-compose -f "$COMPOSE_FILE" down
        
        log_info "恢复数据..."
        tar -xzf "$backup_file"
        
        log_info "启动服务..."
        docker-compose -f "$COMPOSE_FILE" up -d
        
        log_success "数据恢复完成"
    else
        log_info "取消恢复操作"
    fi
}

# 查看系统信息
show_info() {
    echo "=========================================="
    echo "  系统信息"
    echo "=========================================="
    echo "Docker版本: $(docker --version)"
    echo "Docker Compose版本: $(docker-compose --version)"
    echo "系统时间: $(date)"
    echo "系统负载: $(uptime)"
    echo
    
    echo "服务状态:"
    docker-compose -f "$COMPOSE_FILE" ps
    echo
    
    echo "磁盘使用:"
    df -h
    echo
    
    echo "内存使用:"
    free -h
    echo
}

# 显示帮助信息
show_help() {
    echo "社交媒体管理系统 - 管理脚本"
    echo
    echo "用法: $0 <命令> [参数]"
    echo
    echo "命令:"
    echo "  start           启动所有服务"
    echo "  stop            停止所有服务"
    echo "  restart         重启所有服务"
    echo "  rebuild         重新构建并启动服务"
    echo "  status          查看服务状态"
    echo "  logs [service]  查看日志 (可指定服务名)"
    echo "  enter [service] 进入容器 (默认backend)"
    echo "  update          更新代码并重新部署"
    echo "  cleanup         清理Docker资源"
    echo "  backup          备份数据"
    echo "  restore <file>  恢复数据"
    echo "  info            显示系统信息"
    echo "  help            显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 start                    # 启动服务"
    echo "  $0 logs backend             # 查看后端日志"
    echo "  $0 enter frontend           # 进入前端容器"
    echo "  $0 restore backup.tar.gz    # 恢复备份"
    echo
}

# 主函数
main() {
    local command="${1:-help}"
    
    case "$command" in
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "rebuild")
            rebuild_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "enter")
            enter_container "$2"
            ;;
        "update")
            update_code
            ;;
        "cleanup")
            cleanup
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            restore_data "$2"
            ;;
        "info")
            show_info
            ;;
        "help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
