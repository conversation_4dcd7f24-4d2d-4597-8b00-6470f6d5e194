import os
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.database import get_db
from app.routers.auth import get_current_user
from app.services.data_download_service import DataDownloadService
from app.models import DataDownloadRecord, PlatformAccount

router = APIRouter()

# Pydantic模型
class DataDownloadRequest(BaseModel):
    start_date: str  # YYYY-MM-DD格式
    end_date: str    # YYYY-MM-DD格式
    account_ids: List[int]  # 账号ID列表
    data_types: List[str]   # 数据类型列表

class DataDownloadResponse(BaseModel):
    success: bool
    task_id: Optional[int] = None
    total_files: Optional[int] = None
    message: Optional[str] = None
    error: Optional[str] = None


@router.post("/start", response_model=DataDownloadResponse)
async def start_download_task(
    request: DataDownloadRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """启动数据下载任务"""
    try:
        result = await DataDownloadService.start_download_task(
            user_id=current_user.id,
            start_date=request.start_date,
            end_date=request.end_date,
            account_ids=request.account_ids,
            data_types=request.data_types
        )
        
        if result["success"]:
            return DataDownloadResponse(
                success=True,
                task_id=result["task_id"],
                total_files=result["total_files"],
                message=result["message"]
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动下载任务失败: {str(e)}"
        )


@router.get("/status/{task_id}")
async def get_download_status(
    task_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取下载任务状态"""
    try:
        result = DataDownloadService.get_download_status(db, task_id, current_user.id)
        
        if result["success"]:
            return result
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result["error"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务状态失败: {str(e)}"
        )


@router.get("/download/{task_id}")
async def download_zip_file(
    task_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """下载ZIP文件"""
    try:
        # 获取任务状态
        result = DataDownloadService.get_download_status(db, task_id, current_user.id)
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result["error"]
            )
        
        # 检查任务是否完成
        if result["status"] != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务尚未完成，无法下载"
            )
        
        # 检查ZIP文件是否存在
        zip_file_path = result.get("zip_file_path")
        if not zip_file_path or not os.path.exists(zip_file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="下载文件不存在或已过期"
            )
        
        # 生成下载文件名
        filename = f"data_download_{task_id}_{result['start_date']}_to_{result['end_date']}.zip"
        
        return FileResponse(
            path=zip_file_path,
            filename=filename,
            media_type='application/zip'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载文件失败: {str(e)}"
        )


@router.get("/history")
async def get_download_history(
    page: int = 1,
    page_size: int = 20,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取下载历史记录"""
    try:
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询总数
        total = db.query(DataDownloadRecord).filter(
            DataDownloadRecord.user_id == current_user.id
        ).count()
        
        # 查询记录
        records = db.query(DataDownloadRecord).filter(
            DataDownloadRecord.user_id == current_user.id
        ).order_by(DataDownloadRecord.created_at.desc()).offset(offset).limit(page_size).all()
        
        data = []
        for record in records:
            # 计算任务耗时
            duration = None
            if record.completed_at and record.created_at:
                duration_seconds = (record.completed_at - record.created_at).total_seconds()
                if duration_seconds < 60:
                    duration = f"{int(duration_seconds)}秒"
                elif duration_seconds < 3600:
                    duration = f"{int(duration_seconds // 60)}分{int(duration_seconds % 60)}秒"
                else:
                    hours = int(duration_seconds // 3600)
                    minutes = int((duration_seconds % 3600) // 60)
                    duration = f"{hours}小时{minutes}分钟"
            
            data.append({
                "id": record.id,
                "start_date": record.start_date.isoformat(),
                "end_date": record.end_date.isoformat(),
                "selected_accounts": record.selected_accounts,
                "selected_data_types": record.selected_data_types,
                "status": record.status,
                "total_files": record.total_files,
                "completed_files": record.completed_files,
                "current_account_name": record.current_account_name,
                "current_step": record.current_step,
                "error_message": record.error_message,
                "created_at": record.created_at.isoformat(),
                "updated_at": record.updated_at.isoformat(),
                "completed_at": record.completed_at.isoformat() if record.completed_at else None,
                "progress_percent": round((record.completed_files / record.total_files) * 100, 1) if record.total_files > 0 else 0,
                "duration": duration,
                "can_download": record.status == "completed" and record.zip_file_path and os.path.exists(record.zip_file_path)
            })
        
        return {
            "success": True,
            "data": data,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取下载历史失败: {str(e)}"
        )


@router.get("/accounts")
async def get_available_accounts(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取可用的账号列表"""
    try:
        accounts = db.query(PlatformAccount).filter(
            PlatformAccount.user_id == current_user.id,
            PlatformAccount.platform.in_(["wechat_mp", "wechat_service"]),
            PlatformAccount.login_status == True
        ).all()
        
        return {
            "success": True,
            "accounts": [
                {
                    "id": account.id,
                    "name": account.name,
                    "platform": account.platform
                }
                for account in accounts
            ]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取账号列表失败: {str(e)}"
        )


@router.get("/data-types")
async def get_data_types():
    """获取可用的数据类型列表"""
    return {
        "success": True,
        "data_types": DataDownloadService.DATA_TYPE_CONFIG
    }
