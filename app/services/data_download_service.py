import asyncio
import os
import shutil
import zipfile
from datetime import datetime, date
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models import DataDownloadRecord, PlatformAccount, User
from app.services.wechat_service import WeChatMPService
from app.database import SessionLocal
from app.utils.excel_parser import ExcelDataParser
import logging

logger = logging.getLogger(__name__)


class DataDownloadService:
    """数据下载服务类"""
    
    # 数据类型配置
    DATA_TYPE_CONFIG = {
        "content_trend": "内容数据趋势明细",
        "content_source": "内容流量来源明细",
        "content_detail": "内容已通知内容明细",
        "user_channel": "用户增长明细",
        "user_source": "用户来源明细"
    }

    # Excel下载支持的数据类型
    EXCEL_DOWNLOAD_TYPES = ["content_trend", "content_source", "content_detail", "user_channel"]

    # AJAX获取的数据类型
    AJAX_DATA_TYPES = ["user_source"]
    
    # 临时下载目录
    DOWNLOAD_BASE_DIR = "temp_downloads"
    
    @staticmethod
    def validate_download_request(start_date: str, end_date: str, account_ids: List[int], 
                                data_types: List[str]) -> Dict[str, Any]:
        """验证下载请求参数
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            account_ids: 账号ID列表
            data_types: 数据类型列表
            
        Returns:
            验证结果
        """
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            if start > end:
                return {"success": False, "error": "开始日期不能晚于结束日期"}
            
            if (end - start).days > 90:
                return {"success": False, "error": "日期范围不能超过90天"}
            
            if not account_ids:
                return {"success": False, "error": "请至少选择一个账号"}
                
            if not data_types:
                return {"success": False, "error": "请至少选择一种数据类型"}
                
            # 验证数据类型
            invalid_types = [dt for dt in data_types if dt not in DataDownloadService.DATA_TYPE_CONFIG]
            if invalid_types:
                return {"success": False, "error": f"不支持的数据类型: {', '.join(invalid_types)}"}
            
            return {"success": True, "start_date": start, "end_date": end}
            
        except ValueError as e:
            return {"success": False, "error": f"日期格式错误: {str(e)}"}
    
    @staticmethod
    def get_user_accounts(db: Session, user_id: int, account_ids: List[int]) -> List[PlatformAccount]:
        """获取用户的指定账号列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            account_ids: 账号ID列表
            
        Returns:
            账号列表
        """
        return db.query(PlatformAccount).filter(
            and_(
                PlatformAccount.id.in_(account_ids),
                PlatformAccount.user_id == user_id,
                PlatformAccount.platform.in_(["wechat_mp", "wechat_service"]),
                PlatformAccount.login_status == True
            )
        ).all()
    
    @staticmethod
    def create_download_record(db: Session, user_id: int, start_date: date, end_date: date,
                             account_ids: List[int], data_types: List[str]) -> DataDownloadRecord:
        """创建下载记录
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            start_date: 开始日期
            end_date: 结束日期
            account_ids: 账号ID列表
            data_types: 数据类型列表
            
        Returns:
            下载记录对象
        """
        # 计算总文件数
        total_files = len(account_ids) * len(data_types)
        
        record = DataDownloadRecord(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
            selected_accounts=account_ids,
            selected_data_types=data_types,
            status='running',
            total_files=total_files,
            completed_files=0
        )
        db.add(record)
        db.commit()
        db.refresh(record)
        return record
    
    @staticmethod
    def update_record_status(db: Session, record_id: int, **kwargs) -> bool:
        """更新记录状态
        
        Args:
            db: 数据库会话
            record_id: 记录ID
            **kwargs: 要更新的字段
            
        Returns:
            更新是否成功
        """
        try:
            record = db.query(DataDownloadRecord).filter(DataDownloadRecord.id == record_id).first()
            if not record:
                return False
                
            for key, value in kwargs.items():
                if hasattr(record, key):
                    setattr(record, key, value)
            
            record.updated_at = datetime.utcnow()
            
            if kwargs.get('status') == 'completed':
                record.completed_at = datetime.utcnow()
                
            db.commit()
            return True
            
        except Exception as e:
            logger.error(f"更新记录状态失败: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    def create_download_directory(record_id: int) -> str:
        """创建下载目录

        Args:
            record_id: 记录ID

        Returns:
            下载目录路径
        """
        # 确保基础目录存在
        os.makedirs(DataDownloadService.DOWNLOAD_BASE_DIR, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        download_dir = os.path.join(DataDownloadService.DOWNLOAD_BASE_DIR, f"download_{record_id}_{timestamp}")

        os.makedirs(download_dir, exist_ok=True)
        return download_dir
    
    @staticmethod
    def generate_filename(account_name: str, data_type: str, start_date: str, end_date: str) -> str:
        """生成文件名

        Args:
            account_name: 账号名称
            data_type: 数据类型
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            文件名
        """
        data_type_name = DataDownloadService.DATA_TYPE_CONFIG.get(data_type, data_type)
        # 清理文件名中的特殊字符
        safe_account_name = "".join(c for c in account_name if c.isalnum() or c in (' ', '-', '_')).strip()
        return f"{safe_account_name}+{data_type_name}+{start_date}_to_{end_date}.xlsx"

    @staticmethod
    async def download_account_data(account_id: int, data_types: List[str], start_date: str,
                                  end_date: str, download_dir: str, record_id: int) -> Dict[str, Any]:
        """下载单个账号的多个数据类型

        Args:
            account_id: 账号ID
            data_types: 数据类型列表
            start_date: 开始日期
            end_date: 结束日期
            download_dir: 下载目录
            record_id: 下载记录ID

        Returns:
            下载结果
        """
        db = SessionLocal()
        try:
            # 获取账号信息
            account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
            if not account:
                return {"success": False, "error": f"账号ID {account_id} 不存在"}

            # 创建微信服务实例（只创建一次）
            wechat_service = WeChatMPService(account_id=account_id)

            # 尝试加载已保存的登录状态
            if not await wechat_service.load_login_state():
                error_msg = f"账号 {account.name} 登录状态恢复失败，请重新登录"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}

            downloaded_files = []
            failed_files = []

            # 分别处理Excel下载和AJAX获取的数据类型
            excel_types = [dt for dt in data_types if dt in DataDownloadService.EXCEL_DOWNLOAD_TYPES]
            ajax_types = [dt for dt in data_types if dt in DataDownloadService.AJAX_DATA_TYPES]

            # 处理Excel下载的数据类型
            for data_type in excel_types:
                try:
                    logger.info(f"下载账号 {account.name} 的 {data_type} 数据")

                    # 下载Excel数据（设置auto_import=False避免自动导入）
                    excel_data = await wechat_service.download_data_excel(
                        begin_date=start_date,
                        end_date=end_date,
                        data_type=data_type,
                        auto_import=False
                    )

                    if not excel_data:
                        failed_files.append({
                            "data_type": data_type,
                            "error": "下载数据失败"
                        })
                        continue

                    # 生成文件名
                    filename = DataDownloadService.generate_filename(
                        account.name, data_type, start_date, end_date
                    )

                    # 保存文件
                    parser = ExcelDataParser()
                    if not parser.save_excel_file(download_dir, filename, excel_data):
                        failed_files.append({
                            "data_type": data_type,
                            "error": "保存文件失败"
                        })
                        continue

                    downloaded_files.append({
                        "filename": filename,
                        "data_type": data_type
                    })

                    logger.info(f"成功下载: {filename}")

                except Exception as e:
                    error_msg = f"下载数据类型 {data_type} 时发生错误: {str(e)}"
                    logger.error(error_msg)
                    failed_files.append({
                        "data_type": data_type,
                        "error": error_msg
                    })

            # 处理AJAX获取的数据类型（如user_source）
            for data_type in ajax_types:
                try:
                    logger.info(f"获取账号 {account.name} 的 {data_type} 数据")

                    if data_type == "user_source":
                        # 如果没有进行过Excel下载，需要先确保页面已导航到微信公众号
                        if not excel_types:
                            # 确保页面已初始化并导航到微信公众号
                            if not wechat_service.page:
                                logger.error("页面未初始化，无法获取用户来源数据")
                                failed_files.append({
                                    "data_type": data_type,
                                    "error": "页面未初始化"
                                })
                                continue

                            # 检查当前URL，如果不在微信公众号页面，先导航过去
                            current_url = wechat_service.page.url
                            logger.info(f"当前URL: {current_url}")

                            if "mp.weixin.qq.com" not in current_url or "token=" not in current_url:
                                logger.info("当前不在微信公众号页面，正在导航到微信公众号主页...")
                                try:
                                    await wechat_service.page.goto("https://mp.weixin.qq.com",
                                                               wait_until="networkidle", timeout=15000)
                                    await asyncio.sleep(3)  # 等待自动跳转完成

                                    # 更新当前URL
                                    current_url = wechat_service.page.url
                                    logger.info(f"导航后的URL: {current_url}")

                                    # 再次检查是否有token
                                    if "token=" not in current_url:
                                        logger.error("导航后仍未检测到登录状态，可能登录已过期")
                                        failed_files.append({
                                            "data_type": data_type,
                                            "error": "登录状态已过期"
                                        })
                                        continue

                                except Exception as nav_e:
                                    logger.error(f"导航到微信公众号页面失败: {nav_e}")
                                    failed_files.append({
                                        "data_type": data_type,
                                        "error": f"导航失败: {str(nav_e)}"
                                    })
                                    continue

                        # 获取用户来源数据
                        user_source_data = await wechat_service.fetch_user_source_data(
                            begin_date=start_date,
                            end_date=end_date
                        )

                        if not user_source_data:
                            failed_files.append({
                                "data_type": data_type,
                                "error": "获取用户来源数据失败"
                            })
                            continue

                        # 将数据转换为Excel格式并保存
                        excel_data = DataDownloadService.convert_user_source_to_excel(
                            user_source_data, account.name, start_date, end_date
                        )

                        if not excel_data:
                            failed_files.append({
                                "data_type": data_type,
                                "error": "转换用户来源数据为Excel失败"
                            })
                            continue

                        # 生成文件名
                        filename = DataDownloadService.generate_filename(
                            account.name, data_type, start_date, end_date
                        )

                        # 保存文件
                        parser = ExcelDataParser()
                        if not parser.save_excel_file(download_dir, filename, excel_data):
                            failed_files.append({
                                "data_type": data_type,
                                "error": "保存文件失败"
                            })
                            continue

                        downloaded_files.append({
                            "filename": filename,
                            "data_type": data_type
                        })

                        logger.info(f"成功获取并保存: {filename}")

                except Exception as e:
                    error_msg = f"获取数据类型 {data_type} 时发生错误: {str(e)}"
                    logger.error(error_msg)
                    failed_files.append({
                        "data_type": data_type,
                        "error": error_msg
                    })

            return {
                "success": True,
                "account_name": account.name,
                "downloaded_files": downloaded_files,
                "failed_files": failed_files,
                "total_downloaded": len(downloaded_files),
                "total_failed": len(failed_files)
            }

        except Exception as e:
            error_msg = f"下载账号 {account_id} 时发生错误: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        finally:
            db.close()

    @staticmethod
    def create_zip_file(download_dir: str, record_id: int) -> str:
        """创建ZIP压缩文件

        Args:
            download_dir: 下载目录
            record_id: 记录ID

        Returns:
            ZIP文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"data_download_{record_id}_{timestamp}.zip"
        zip_path = os.path.join(DataDownloadService.DOWNLOAD_BASE_DIR, zip_filename)

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(download_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 在ZIP中保持目录结构
                    arcname = os.path.relpath(file_path, download_dir)
                    zipf.write(file_path, arcname)

        return zip_path

    @staticmethod
    def convert_user_source_to_excel(user_source_data: dict, account_name: str,
                                   start_date: str, end_date: str) -> Optional[bytes]:
        """将用户来源数据转换为Excel格式

        Args:
            user_source_data: 用户来源数据
            account_name: 账号名称
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Excel文件的二进制数据
        """
        try:
            import pandas as pd
            from io import BytesIO

            # 解析用户来源数据
            category_list = user_source_data.get('category_list', [])

            # 准备Excel数据
            excel_data = []

            for category in category_list:
                user_source = category.get('user_source')
                data_list = category.get('list', [])

                if user_source is None:
                    continue

                for item in data_list:
                    excel_data.append({
                        '账号名称': account_name,
                        '用户来源': user_source,
                        '日期': item.get('date', ''),
                        '新增用户': item.get('new_user', 0),
                        '取消关注用户': item.get('cancel_user', 0),
                        '净增用户': item.get('netgain_user', 0),
                        '累计用户': item.get('cumulate_user', 0)
                    })

            if not excel_data:
                logger.warning("用户来源数据为空")
                return None

            # 创建DataFrame
            df = pd.DataFrame(excel_data)

            # 将DataFrame写入Excel
            buffer = BytesIO()
            with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='用户来源明细', index=False)

            buffer.seek(0)
            excel_bytes = buffer.getvalue()
            buffer.close()

            logger.info(f"用户来源数据转换为Excel成功，数据行数: {len(excel_data)}")
            return excel_bytes

        except Exception as e:
            logger.error(f"转换用户来源数据为Excel失败: {str(e)}")
            return None

    @staticmethod
    async def start_download_task(user_id: int, start_date: str, end_date: str,
                                account_ids: List[int], data_types: List[str]) -> Dict[str, Any]:
        """启动数据下载任务

        Args:
            user_id: 用户ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            account_ids: 账号ID列表
            data_types: 数据类型列表

        Returns:
            任务启动结果，包含任务ID
        """
        # 验证请求参数
        validation = DataDownloadService.validate_download_request(
            start_date, end_date, account_ids, data_types
        )
        if not validation["success"]:
            return validation

        db = SessionLocal()
        try:
            # 检查是否有正在运行的任务
            running_task = db.query(DataDownloadRecord).filter(
                and_(
                    DataDownloadRecord.user_id == user_id,
                    DataDownloadRecord.status == 'running'
                )
            ).first()

            if running_task:
                return {
                    "success": False,
                    "error": "您已有数据下载任务正在运行，请等待完成后再试"
                }

            # 验证账号权限
            accounts = DataDownloadService.get_user_accounts(db, user_id, account_ids)
            if not accounts:
                return {
                    "success": False,
                    "error": "没有找到可用的已登录账号"
                }

            # 创建下载记录
            record = DataDownloadService.create_download_record(
                db, user_id, validation["start_date"], validation["end_date"],
                account_ids, data_types
            )

            # 启动异步任务
            asyncio.create_task(
                DataDownloadService._execute_download_task(
                    record.id, start_date, end_date, account_ids, data_types
                )
            )

            return {
                "success": True,
                "task_id": record.id,
                "total_files": len(account_ids) * len(data_types),
                "message": "数据下载任务已启动"
            }

        except Exception as e:
            logger.error(f"启动数据下载任务失败: {str(e)}")
            return {"success": False, "error": f"启动任务失败: {str(e)}"}
        finally:
            db.close()

    @staticmethod
    async def _execute_download_task(record_id: int, start_date: str, end_date: str,
                                   account_ids: List[int], data_types: List[str]):
        """执行数据下载任务（内部方法）

        Args:
            record_id: 下载记录ID
            start_date: 开始日期
            end_date: 结束日期
            account_ids: 账号ID列表
            data_types: 数据类型列表
        """
        db = SessionLocal()
        try:
            logger.info(f"开始执行数据下载任务 {record_id}")

            # 创建下载目录
            download_dir = DataDownloadService.create_download_directory(record_id)

            DataDownloadService.update_record_status(
                db, record_id,
                download_path=download_dir,
                current_step="创建下载目录"
            )

            # 获取账号信息
            accounts = db.query(PlatformAccount).filter(
                PlatformAccount.id.in_(account_ids)
            ).all()

            completed_count = 0
            failed_files = []

            # 逐个处理账号
            for account in accounts:
                DataDownloadService.update_record_status(
                    db, record_id,
                    current_account_name=account.name,
                    current_step=f"下载账号 {account.name} 的数据"
                )

                # 下载该账号的所有数据类型
                result = await DataDownloadService.download_account_data(
                    account.id, data_types, start_date, end_date, download_dir, record_id
                )

                if result["success"]:
                    # 更新成功下载的文件数
                    completed_count += result["total_downloaded"]

                    # 记录失败的文件
                    for failed_file in result["failed_files"]:
                        failed_files.append({
                            "account_name": account.name,
                            "data_type": failed_file["data_type"],
                            "error": failed_file["error"]
                        })

                    logger.info(f"账号 {account.name} 下载完成: 成功 {result['total_downloaded']} 个，失败 {result['total_failed']} 个")
                else:
                    # 整个账号下载失败
                    for data_type in data_types:
                        failed_files.append({
                            "account_name": account.name,
                            "data_type": data_type,
                            "error": result.get("error", "未知错误")
                        })
                    logger.error(f"账号 {account.name} 下载失败: {result.get('error')}")

                # 更新进度
                DataDownloadService.update_record_status(
                    db, record_id, completed_files=completed_count
                )

                # 账号间添加延迟，避免请求过于频繁
                await asyncio.sleep(2)

            # 创建ZIP文件
            DataDownloadService.update_record_status(
                db, record_id, current_step="创建压缩文件"
            )

            zip_path = DataDownloadService.create_zip_file(download_dir, record_id)

            # 完成任务
            if failed_files:
                failed_info = ', '.join([f"{f['account_name']}-{f['data_type']}({f['error']})" for f in failed_files])
                error_summary = f"部分文件下载失败: {failed_info}"
                DataDownloadService.update_record_status(
                    db, record_id,
                    status='completed',
                    current_step="完成（部分失败）",
                    zip_file_path=zip_path,
                    error_message=error_summary
                )
                logger.warning(f"任务 {record_id} 完成，但有 {len(failed_files)} 个文件失败")
            else:
                DataDownloadService.update_record_status(
                    db, record_id,
                    status='completed',
                    current_step="全部完成",
                    zip_file_path=zip_path
                )
                logger.info(f"任务 {record_id} 全部完成")

            # 清理临时目录
            try:
                shutil.rmtree(download_dir)
                logger.info(f"已清理临时目录: {download_dir}")
            except Exception as e:
                logger.warning(f"清理临时目录失败: {e}")

        except Exception as e:
            logger.error(f"执行下载任务 {record_id} 时发生错误: {str(e)}")
            DataDownloadService.update_record_status(
                db, record_id,
                status='failed',
                error_message=f"任务执行失败: {str(e)}"
            )
        finally:
            db.close()

    @staticmethod
    def get_download_status(db: Session, task_id: int, user_id: int) -> Dict[str, Any]:
        """获取下载任务状态

        Args:
            db: 数据库会话
            task_id: 任务ID
            user_id: 用户ID

        Returns:
            任务状态信息
        """
        try:
            record = db.query(DataDownloadRecord).filter(
                and_(
                    DataDownloadRecord.id == task_id,
                    DataDownloadRecord.user_id == user_id
                )
            ).first()

            if not record:
                return {"success": False, "error": "任务不存在或无权限访问"}

            return {
                "success": True,
                "task_id": record.id,
                "start_date": record.start_date.isoformat(),
                "end_date": record.end_date.isoformat(),
                "selected_accounts": record.selected_accounts,
                "selected_data_types": record.selected_data_types,
                "status": record.status,
                "total_files": record.total_files,
                "completed_files": record.completed_files,
                "current_account_name": record.current_account_name,
                "current_step": record.current_step,
                "error_message": record.error_message,
                "created_at": record.created_at.isoformat(),
                "updated_at": record.updated_at.isoformat(),
                "completed_at": record.completed_at.isoformat() if record.completed_at else None,
                "progress_percent": round((record.completed_files / record.total_files) * 100, 1) if record.total_files > 0 else 0,
                "zip_file_path": record.zip_file_path
            }

        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            return {"success": False, "error": f"获取任务状态失败: {str(e)}"}
