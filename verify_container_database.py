#!/usr/bin/env python3
"""
验证容器数据库配置脚本
检查Docker容器是否正确使用了根目录的数据库文件
"""
import requests
import json
import subprocess
import os

def check_container_status():
    """检查容器状态"""
    print("=== 检查容器状态 ===")
    try:
        result = subprocess.run(['docker', 'compose', 'ps'], 
                              capture_output=True, text=True, check=True)
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"❌ 检查容器状态失败: {e}")
    print()

def check_database_mount():
    """检查数据库文件挂载"""
    print("=== 检查数据库文件挂载 ===")
    try:
        # 检查容器内的数据库文件
        result = subprocess.run([
            'docker', 'exec', 'social_media_backend', 
            'ls', '-la', '/app/'
        ], capture_output=True, text=True, check=True)
        
        print("容器内 /app/ 目录内容:")
        print(result.stdout)
        
        # 检查数据库文件是否存在
        result = subprocess.run([
            'docker', 'exec', 'social_media_backend', 
            'ls', '-la', '/app/social_media.db'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 容器内数据库文件存在:")
            print(result.stdout)
        else:
            print("❌ 容器内数据库文件不存在")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 检查数据库挂载失败: {e}")
    print()

def check_database_content_in_container():
    """检查容器内数据库内容"""
    print("=== 检查容器内数据库内容 ===")
    try:
        # 在容器内执行SQLite命令查看用户数据
        sql_command = "SELECT COUNT(*) as user_count FROM users;"
        result = subprocess.run([
            'docker', 'exec', 'social_media_backend',
            'sqlite3', '/app/social_media.db', sql_command
        ], capture_output=True, text=True, check=True)
        
        user_count = result.stdout.strip()
        print(f"容器内数据库用户数量: {user_count}")
        
        # 查看用户列表
        sql_command = "SELECT id, username, email FROM users LIMIT 5;"
        result = subprocess.run([
            'docker', 'exec', 'social_media_backend',
            'sqlite3', '/app/social_media.db', sql_command
        ], capture_output=True, text=True, check=True)
        
        print("容器内数据库用户列表:")
        print(result.stdout)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 检查容器内数据库内容失败: {e}")
    print()

def test_api_with_known_user():
    """使用已知用户测试API"""
    print("=== 测试API登录 ===")
    
    # 从本地数据库获取的已知用户
    test_users = [
        ("testuser", "testpassword123"),
        ("admin", "admin123"),  # 可能的密码
        ("admin", "password"),  # 可能的密码
        ("admin", "admin"),     # 可能的密码
    ]
    
    api_url = "http://localhost:8000/api/auth/login"
    
    for username, password in test_users:
        try:
            response = requests.post(api_url, json={
                "username": username,
                "password": password
            }, timeout=10)
            
            print(f"测试用户 {username}:")
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ 登录成功!")
                print(f"  用户信息: {data.get('user', {})}")
                return True
            elif response.status_code == 401:
                print(f"  ❌ 认证失败 (401)")
                try:
                    error_data = response.json()
                    print(f"  错误信息: {error_data.get('detail', 'Unknown error')}")
                except:
                    print(f"  错误信息: {response.text}")
            else:
                print(f"  ❌ 其他错误: {response.status_code}")
                print(f"  响应: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ 请求失败: {e}")
    
    print()
    return False

def check_environment_variables():
    """检查容器环境变量"""
    print("=== 检查容器环境变量 ===")
    try:
        result = subprocess.run([
            'docker', 'exec', 'social_media_backend',
            'env'
        ], capture_output=True, text=True, check=True)
        
        env_vars = result.stdout.split('\n')
        relevant_vars = [var for var in env_vars if 'DATABASE' in var or 'SECRET' in var]
        
        print("相关环境变量:")
        for var in relevant_vars:
            if var.strip():
                print(f"  {var}")
                
    except subprocess.CalledProcessError as e:
        print(f"❌ 检查环境变量失败: {e}")
    print()

def compare_database_files():
    """比较宿主机和容器内的数据库文件"""
    print("=== 比较数据库文件 ===")
    
    # 检查宿主机数据库文件
    if os.path.exists('./social_media.db'):
        host_size = os.path.getsize('./social_media.db')
        print(f"宿主机数据库文件大小: {host_size} bytes")
    else:
        print("❌ 宿主机数据库文件不存在")
        return
    
    try:
        # 检查容器内数据库文件大小
        result = subprocess.run([
            'docker', 'exec', 'social_media_backend',
            'stat', '-c', '%s', '/app/social_media.db'
        ], capture_output=True, text=True, check=True)
        
        container_size = int(result.stdout.strip())
        print(f"容器内数据库文件大小: {container_size} bytes")
        
        if host_size == container_size:
            print("✅ 文件大小匹配，挂载可能正确")
        else:
            print("❌ 文件大小不匹配，挂载可能有问题")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 检查容器内数据库文件大小失败: {e}")
    
    print()

def main():
    print("🔍 验证容器数据库配置")
    print("=" * 50)
    
    check_container_status()
    check_database_mount()
    compare_database_files()
    check_database_content_in_container()
    check_environment_variables()
    
    # 测试API
    success = test_api_with_known_user()
    
    if not success:
        print("\n💡 建议:")
        print("1. 检查数据库文件是否正确挂载")
        print("2. 检查用户密码是否正确")
        print("3. 查看容器日志: docker compose logs backend")
        print("4. 重新启动服务: docker compose restart")

if __name__ == "__main__":
    main()
