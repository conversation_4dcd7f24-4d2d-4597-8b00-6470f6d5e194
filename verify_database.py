#!/usr/bin/env python3
"""
数据库验证脚本 - 检查用户数据和数据库连接
"""
import sqlite3
import os
from datetime import datetime

def check_database_file():
    """检查数据库文件是否存在"""
    db_files = [
        "./social_media.db",
        "./data/social_media.db",
        "./social_media_manager.db"
    ]
    
    print("=== 检查数据库文件 ===")
    for db_file in db_files:
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            mtime = datetime.fromtimestamp(os.path.getmtime(db_file))
            print(f"✅ {db_file} 存在 (大小: {size} bytes, 修改时间: {mtime})")
        else:
            print(f"❌ {db_file} 不存在")
    print()

def check_database_content(db_path):
    """检查数据库内容"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件 {db_path} 不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"=== 检查数据库内容: {db_path} ===")
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"数据库中的表: {[table[0] for table in tables]}")
        
        # 检查users表
        if ('users',) in tables:
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"用户总数: {user_count}")
            
            if user_count > 0:
                cursor.execute("SELECT id, username, email, created_at FROM users LIMIT 5")
                users = cursor.fetchall()
                print("前5个用户:")
                for user in users:
                    print(f"  ID: {user[0]}, 用户名: {user[1]}, 邮箱: {user[2]}, 创建时间: {user[3]}")
        else:
            print("❌ users表不存在")
        
        # 检查platform_accounts表
        if ('platform_accounts',) in tables:
            cursor.execute("SELECT COUNT(*) FROM platform_accounts")
            account_count = cursor.fetchone()[0]
            print(f"平台账号总数: {account_count}")
            
            if account_count > 0:
                cursor.execute("SELECT id, name, platform, user_id, login_status FROM platform_accounts LIMIT 5")
                accounts = cursor.fetchall()
                print("前5个平台账号:")
                for account in accounts:
                    print(f"  ID: {account[0]}, 名称: {account[1]}, 平台: {account[2]}, 用户ID: {account[3]}, 登录状态: {account[4]}")
        else:
            print("❌ platform_accounts表不存在")
        
        conn.close()
        print()
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")
        print()

def test_user_authentication():
    """测试用户认证"""
    print("=== 测试用户认证 ===")
    
    # 测试常见的用户名
    test_users = [
        ("admin", "admin"),
        ("testuser", "testpassword123"),
        ("user", "password"),
        ("test", "test123")
    ]
    
    db_path = "./social_media.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件 {db_path} 不存在，无法测试认证")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        for username, password in test_users:
            cursor.execute("SELECT id, username, hashed_password FROM users WHERE username = ?", (username,))
            user = cursor.fetchone()
            
            if user:
                print(f"✅ 找到用户: {username} (ID: {user[0]})")
                print(f"   密码哈希: {user[2][:50]}...")
                
                # 这里我们不能直接验证密码，因为需要bcrypt库
                # 但我们可以检查密码哈希是否存在
                if user[2]:
                    print(f"   密码哈希存在，长度: {len(user[2])}")
                else:
                    print(f"   ❌ 密码哈希为空")
            else:
                print(f"❌ 未找到用户: {username}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 测试认证时出错: {e}")
    
    print()

def create_test_user():
    """创建测试用户"""
    print("=== 创建测试用户 ===")
    
    db_path = "./social_media.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件 {db_path} 不存在")
        return
    
    try:
        # 导入必要的库来创建密码哈希
        from passlib.context import CryptContext
        
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查testuser是否已存在
        cursor.execute("SELECT id FROM users WHERE username = ?", ("testuser",))
        if cursor.fetchone():
            print("✅ testuser 已存在")
        else:
            # 创建testuser
            hashed_password = pwd_context.hash("testpassword123")
            cursor.execute(
                "INSERT INTO users (username, email, hashed_password, created_at) VALUES (?, ?, ?, ?)",
                ("testuser", "<EMAIL>", hashed_password, datetime.utcnow())
            )
            conn.commit()
            print("✅ 创建了测试用户: testuser / testpassword123")
        
        conn.close()
        
    except ImportError:
        print("❌ 无法导入passlib库，请先安装: pip install passlib[bcrypt]")
    except Exception as e:
        print(f"❌ 创建测试用户时出错: {e}")
    
    print()

if __name__ == "__main__":
    print("🔍 数据库验证脚本")
    print("=" * 50)
    
    check_database_file()
    check_database_content("./social_media.db")
    check_database_content("./data/social_media.db")
    test_user_authentication()
    create_test_user()
    
    print("验证完成！")
