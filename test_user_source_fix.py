#!/usr/bin/env python3
"""
测试用户来源数据类型修复的脚本
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_data_types_api():
    """测试数据类型API，确认user_source已恢复"""
    
    print("=== 测试数据类型配置 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/data-download/data-types")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            data_types = data.get("data_types", {})
            
            print("可用的数据类型:")
            for key, value in data_types.items():
                print(f"  {key}: {value}")
            
            # 检查是否包含user_source
            if "user_source" in data_types:
                print("✅ 正确：包含user_source")
            else:
                print("❌ 错误：缺少user_source")
                
            # 检查是否包含所有预期的类型
            expected_types = ["content_trend", "content_source", "content_detail", "user_channel", "user_source"]
            missing_types = [t for t in expected_types if t not in data_types]
            
            if missing_types:
                print(f"❌ 缺少数据类型: {missing_types}")
            else:
                print("✅ 所有预期的数据类型都存在")
                
        else:
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"请求失败: {e}")

def test_convert_user_source_data():
    """测试用户来源数据转换功能"""
    
    print("\n=== 测试用户来源数据转换 ===")
    
    # 模拟用户来源数据
    mock_data = {
        "category_list": [
            {
                "user_source": "公众号搜索",
                "list": [
                    {
                        "date": "2024-01-01",
                        "new_user": 10,
                        "cancel_user": 2,
                        "netgain_user": 8,
                        "cumulate_user": 100
                    },
                    {
                        "date": "2024-01-02", 
                        "new_user": 15,
                        "cancel_user": 3,
                        "netgain_user": 12,
                        "cumulate_user": 112
                    }
                ]
            },
            {
                "user_source": "扫描二维码",
                "list": [
                    {
                        "date": "2024-01-01",
                        "new_user": 5,
                        "cancel_user": 1,
                        "netgain_user": 4,
                        "cumulate_user": 50
                    }
                ]
            }
        ]
    }
    
    try:
        # 导入转换函数
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app.services.data_download_service import DataDownloadService
        
        # 测试转换
        excel_data = DataDownloadService.convert_user_source_to_excel(
            mock_data, "测试账号", "2024-01-01", "2024-01-02"
        )
        
        if excel_data:
            print("✅ 用户来源数据转换成功")
            print(f"   Excel文件大小: {len(excel_data)} bytes")
            
            # 保存测试文件
            with open("test_user_source.xlsx", "wb") as f:
                f.write(excel_data)
            print("   测试文件已保存为: test_user_source.xlsx")
        else:
            print("❌ 用户来源数据转换失败")
            
    except Exception as e:
        print(f"❌ 转换测试失败: {e}")

if __name__ == "__main__":
    test_data_types_api()
    test_convert_user_source_data()
