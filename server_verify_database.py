#!/usr/bin/env python3
"""
服务器数据库验证脚本
在Ubuntu服务器上运行此脚本来验证Docker容器的数据库配置
"""
import subprocess
import requests
import json
import os
import sqlite3
from datetime import datetime

def run_command(cmd, capture_output=True):
    """运行shell命令"""
    try:
        if isinstance(cmd, str):
            result = subprocess.run(cmd, shell=True, capture_output=capture_output, text=True, check=True)
        else:
            result = subprocess.run(cmd, capture_output=capture_output, text=True, check=True)
        return result.stdout.strip() if capture_output else None
    except subprocess.CalledProcessError as e:
        return None

def check_container_status():
    """检查容器状态"""
    print("=== 检查容器状态 ===")
    result = run_command("docker compose ps")
    if result:
        print(result)
        return "Up" in result
    else:
        print("❌ 无法获取容器状态")
        return False

def check_host_database():
    """检查宿主机数据库文件"""
    print("\n=== 检查宿主机数据库文件 ===")
    db_path = "./social_media.db"
    
    if os.path.exists(db_path):
        size = os.path.getsize(db_path)
        mtime = datetime.fromtimestamp(os.path.getmtime(db_path))
        print(f"✅ 宿主机数据库文件存在")
        print(f"   大小: {size} bytes")
        print(f"   修改时间: {mtime}")
        
        # 检查数据库内容
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"   用户数量: {user_count}")
            
            cursor.execute("SELECT id, username, email FROM users LIMIT 3")
            users = cursor.fetchall()
            print("   用户列表:")
            for user in users:
                print(f"     ID: {user[0]}, 用户名: {user[1]}, 邮箱: {user[2]}")
            conn.close()
            return size, user_count
        except Exception as e:
            print(f"   ❌ 读取数据库内容失败: {e}")
            return size, 0
    else:
        print("❌ 宿主机数据库文件不存在")
        return 0, 0

def check_container_database():
    """检查容器内数据库文件"""
    print("\n=== 检查容器内数据库文件 ===")
    
    # 检查文件是否存在
    result = run_command("docker exec social_media_backend test -f /app/social_media.db")
    if result is not None:
        print("✅ 容器内数据库文件存在")
        
        # 获取文件大小
        size = run_command("docker exec social_media_backend stat -c%s /app/social_media.db")
        if size:
            print(f"   大小: {size} bytes")
            
        # 获取修改时间
        mtime = run_command("docker exec social_media_backend stat -c%y /app/social_media.db")
        if mtime:
            print(f"   修改时间: {mtime}")
            
        return int(size) if size else 0
    else:
        print("❌ 容器内数据库文件不存在")
        return 0

def check_container_database_content():
    """检查容器内数据库内容"""
    print("\n=== 检查容器内数据库内容 ===")
    
    # 检查用户数量
    user_count = run_command('docker exec social_media_backend sqlite3 /app/social_media.db "SELECT COUNT(*) FROM users;"')
    if user_count:
        print(f"✅ 数据库可访问")
        print(f"   用户数量: {user_count}")
        
        # 获取用户列表
        users = run_command('docker exec social_media_backend sqlite3 /app/social_media.db "SELECT id || \',\' || username || \',\' || email FROM users LIMIT 3;"')
        if users:
            print("   用户列表:")
            for line in users.split('\n'):
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 3:
                        print(f"     ID: {parts[0]}, 用户名: {parts[1]}, 邮箱: {parts[2]}")
        return int(user_count)
    else:
        print("❌ 无法访问数据库")
        return 0

def check_environment_variables():
    """检查容器环境变量"""
    print("\n=== 检查容器环境变量 ===")
    
    db_url = run_command("docker exec social_media_backend printenv DATABASE_URL")
    if db_url:
        print(f"DATABASE_URL: {db_url}")
    else:
        print("DATABASE_URL: 未设置")
    
    secret_key = run_command("docker exec social_media_backend printenv SECRET_KEY")
    if secret_key:
        print(f"SECRET_KEY: 已设置 (长度: {len(secret_key)})")
    else:
        print("SECRET_KEY: 未设置")

def test_api_connection():
    """测试API连接"""
    print("\n=== 测试API连接 ===")
    
    try:
        # 测试根路径
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ 后端API可访问")
            print(f"   响应: {response.json()}")
            
            # 测试登录API
            test_users = [
                ("testuser", "testpassword123"),
                ("admin", "admin123"),
                ("admin", "password"),
                ("admin", "admin")
            ]
            
            print("\n测试登录API:")
            for username, password in test_users:
                try:
                    login_response = requests.post(
                        "http://localhost:8000/api/auth/login",
                        json={"username": username, "password": password},
                        timeout=5
                    )
                    
                    print(f"  用户 {username}: 状态码 {login_response.status_code}")
                    
                    if login_response.status_code == 200:
                        data = login_response.json()
                        print(f"    ✅ 登录成功!")
                        print(f"    用户信息: {data.get('user', {})}")
                        return True
                    elif login_response.status_code == 401:
                        error_data = login_response.json()
                        print(f"    ❌ 认证失败: {error_data.get('detail', 'Unknown error')}")
                    else:
                        print(f"    ❌ 其他错误: {login_response.text}")
                        
                except requests.exceptions.RequestException as e:
                    print(f"    ❌ 请求失败: {e}")
            
            return False
        else:
            print(f"❌ 后端API返回错误: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 后端API不可访问: {e}")
        return False

def check_frontend():
    """检查前端访问"""
    print("\n=== 检查前端访问 ===")
    try:
        response = requests.get("http://localhost:3000/", timeout=5)
        if response.status_code == 200:
            print("✅ 前端可访问")
        else:
            print(f"❌ 前端返回错误: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 前端不可访问: {e}")

def check_docker_mounts():
    """检查Docker挂载配置"""
    print("\n=== 检查Docker挂载配置 ===")
    
    # 检查docker-compose.yml配置
    if os.path.exists("docker-compose.yml"):
        with open("docker-compose.yml", 'r') as f:
            content = f.read()
            if "social_media.db:/app/social_media.db" in content:
                print("✅ docker-compose.yml中包含数据库文件挂载配置")
            else:
                print("❌ docker-compose.yml中缺少数据库文件挂载配置")
    
    # 检查容器挂载信息
    mount_info = run_command('docker inspect social_media_backend | grep -A 5 -B 5 "social_media.db"')
    if mount_info:
        print("容器挂载信息:")
        print(mount_info)

def main():
    print("🔍 验证服务器上的容器数据库配置")
    print("=" * 50)
    
    # 检查容器状态
    if not check_container_status():
        print("\n❌ 容器未运行，请先启动服务: docker compose up -d")
        return
    
    # 检查数据库文件
    host_size, host_users = check_host_database()
    container_size = check_container_database()
    container_users = check_container_database_content()
    
    # 比较结果
    print(f"\n=== 数据库对比结果 ===")
    if host_size == container_size and host_users == container_users:
        print("✅ 宿主机和容器数据库文件匹配，挂载正确")
    else:
        print("❌ 宿主机和容器数据库文件不匹配")
        print(f"   宿主机: {host_size} bytes, {host_users} 用户")
        print(f"   容器内: {container_size} bytes, {container_users} 用户")
    
    # 检查其他配置
    check_environment_variables()
    check_docker_mounts()
    
    # 测试API
    api_success = test_api_connection()
    check_frontend()
    
    print(f"\n{'=' * 50}")
    print("验证完成!")
    
    if not api_success:
        print("\n💡 如果登录仍然失败，可能的原因:")
        print("1. 用户密码不正确")
        print("2. 数据库挂载配置有问题")
        print("3. 环境变量配置错误")
        print("4. 查看容器日志: docker compose logs backend")

if __name__ == "__main__":
    main()
