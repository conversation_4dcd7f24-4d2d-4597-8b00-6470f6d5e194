version: '3.8'

services:
  # Nginx反向代理服务
  nginx:
    image: nginx:alpine
    container_name: social_media_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - app-network
    environment:
      - TZ=Asia/Shanghai

  # 后端API服务
  backend:
    build: .
    container_name: social_media_backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=sqlite:///./data/social_media.db
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - ALGORITHM=${ALGORITHM:-HS256}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      - TZ=Asia/Shanghai
    volumes:
      - ./user_data:/app/user_data
      - ./data:/app/data
      - ./logs:/app/logs
    expose:
      - "8000"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: social_media_frontend
    restart: unless-stopped
    expose:
      - "80"
    depends_on:
      - backend
    networks:
      - app-network
    environment:
      - TZ=Asia/Shanghai

  # # 可选：Redis缓存服务（用于会话管理和缓存）
  # redis:
  #   image: redis:7-alpine
  #   container_name: social_media_redis
  #   restart: unless-stopped
  #   command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
  #   volumes:
  #     - redis_data:/data
  #   expose:
  #     - "6379"
  #   networks:
  #     - app-network
  #   environment:
  #     - TZ=Asia/Shanghai

  # 可选：数据库服务（如果需要MySQL替代SQLite）
  # mysql:
  #   image: mysql:8.4
  #   container_name: social_media_mysql
  #   restart: unless-stopped
  #   environment:
  #     - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root123}
  #     - MYSQL_DATABASE=social_media_manager
  #     - MYSQL_USER=app_user
  #     - MYSQL_PASSWORD=${MYSQL_PASSWORD:-app123}
  #     - TZ=Asia/Shanghai
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #     - ./mysql/init:/docker-entrypoint-initdb.d
  #   expose:
  #     - "3306"
  #   networks:
  #     - app-network

volumes:
  # redis_data:
  # mysql_data:

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
