import React, { useState, useEffect } from 'react';
import { Layout, Menu, Table, Card, Select, Input, Button, message, Spin, Tag, Space } from 'antd';
import { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons';
import { useSearchParams } from 'react-router-dom';
import api from '../services/api';

const { Sider, Content } = Layout;
const { Search } = Input;
const { Option } = Select;

interface Account {
  id: number;
  name: string;
  login_status: boolean;
  last_login_time: string | null;
  created_at: string;
}

interface DataItem {
  id: number;
  account_id: number;
  [key: string]: any;
}

interface DataConfig {
  name: string;
  description: string;
  columns: Array<{
    key: string;
    title: string;
    type: string;
  }>;
}

interface DataTypeConfig {
  [key: string]: DataConfig;
}

const DataDetails: React.FC = () => {
  const [searchParams] = useSearchParams();

  // 从URL参数获取初始值
  const initialPlatform = searchParams.get('platform') || 'wechat_mp';
  const initialDataType = searchParams.get('type') || 'content_trend';

  const [selectedPlatform, setSelectedPlatform] = useState<string>(initialPlatform);
  const [selectedDataType, setSelectedDataType] = useState<string>(initialDataType);
  const [selectedAccount, setSelectedAccount] = useState<number | string | null>('all');
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [dataList, setDataList] = useState<DataItem[]>([]);
  const [dataConfig, setDataConfig] = useState<DataTypeConfig>({});
  const [loading, setLoading] = useState(false);
  const [accountSummary, setAccountSummary] = useState<any>(null);
  const [growthSummary, setGrowthSummary] = useState<any>(null);
  const [overviewLoading, setOverviewLoading] = useState(false);
  const [currentDataRange, setCurrentDataRange] = useState<any>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [searchText, setSearchText] = useState<string>('');

  // 平台配置
  const platformConfig = {
    wechat_mp: {
      name: '微信公众号',
      dataTypes: {
        content_trend: '内容数据趋势明细',
        content_source: '内容流量来源明细',
        content_detail: '内容已通知内容明细',
        user_channel: '用户增长明细',
        user_source: '用户来源明细'
      }
    },
    wechat_channels: {
      name: '视频号',
      dataTypes: {}
    },
    xiaohongshu: {
      name: '小红书',
      dataTypes: {}
    }
  };

  // 菜单项
  const menuItems = [
    {
      key: 'wechat_mp',
      label: '微信公众号',
      children: [
        { key: 'wechat_mp_overview', label: '总览' },
        { key: 'wechat_mp_content_trend', label: '内容数据趋势明细' },
        { key: 'wechat_mp_content_source', label: '内容流量来源明细' },
        { key: 'wechat_mp_content_detail', label: '内容已通知内容明细' },
        { key: 'wechat_mp_user_channel', label: '用户增长明细' },
        { key: 'wechat_mp_user_source', label: '用户来源明细' }
      ]
    },
    {
      key: 'wechat_channels',
      label: '视频号',
      disabled: true,
      children: [
        { key: 'wechat_channels_placeholder', label: '敬请期待', disabled: true }
      ]
    },
    {
      key: 'xiaohongshu',
      label: '小红书',
      disabled: true,
      children: [
        { key: 'xiaohongshu_placeholder', label: '敬请期待', disabled: true }
      ]
    }
  ];

  useEffect(() => {
    fetchDataConfig();
    fetchAccounts();
  }, []);

  // 处理URL参数变化
  useEffect(() => {
    const platform = searchParams.get('platform');
    const type = searchParams.get('type');

    if (platform) {
      setSelectedPlatform(platform);
    }
    if (type) {
      setSelectedDataType(type);
    }
  }, [searchParams]);

  useEffect(() => {
    if (selectedAccount && selectedDataType) {
      if (selectedDataType === 'overview') {
        fetchOverviewData();
      } else {
        fetchDataList();
      }
    }
  }, [selectedAccount, selectedDataType, pagination.current, pagination.pageSize, searchText]);

  const fetchDataConfig = async () => {
    try {
      const response = await api.get('/data-details/wechat-mp/config');
      if (response.data.success) {
        setDataConfig(response.data.data_types);
      }
    } catch (error: any) {
      console.error('获取数据配置失败:', error);
      if (error.response) {
        console.error('错误响应:', error.response.data);
        message.error(`获取数据配置失败: ${error.response.data.detail || error.message}`);
      } else {
        message.error('获取数据配置失败');
      }
    }
  };

  const fetchAccounts = async () => {
    try {
      const response = await api.get('/data-details/wechat-mp/accounts');
      if (response.data.success) {
        setAccounts(response.data.accounts);
        // 默认选择"全部"
        setSelectedAccount('all');
      }
    } catch (error: any) {
      console.error('获取账号列表失败:', error);
      if (error.response) {
        console.error('错误响应:', error.response.data);
        message.error(`获取账号列表失败: ${error.response.data.detail || error.message}`);
      } else {
        message.error('获取账号列表失败');
      }
    }
  };

  const fetchDataList = async () => {
    if (!selectedAccount || !selectedDataType) return;

    setLoading(true);
    try {
      const params: any = {
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchText || undefined,
        sort_field: 'created_at',
        sort_order: 'desc'
      };

      // 只有当选择了具体账号时才传递 account_id 参数
      if (selectedAccount !== 'all') {
        params.account_id = selectedAccount;
      }

      const response = await api.get(`/data-details/wechat-mp/${selectedDataType}`, { params });
      
      if (response.data.success) {
        setDataList(response.data.data);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
          current: response.data.page
        }));
      } else {
        message.error(response.data.error || '获取数据失败');
      }
    } catch (error) {
      message.error('获取数据列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchOverviewData = async () => {
    setOverviewLoading(true);
    try {
      // 并行获取三个数据：账号汇总、增长汇总、当前数据范围
      const [accountSummaryResponse, growthSummaryResponse, dataRangeResponse] = await Promise.all([
        api.get('/data-details/wechat-mp/overview/account-summary'),
        api.get('/data-details/wechat-mp/overview/growth-summary'),
        api.get('/data-update/current-range')
      ]);

      if (accountSummaryResponse.data.success) {
        setAccountSummary(accountSummaryResponse.data);
      } else {
        message.error('获取账号汇总数据失败');
      }

      if (growthSummaryResponse.data.success) {
        setGrowthSummary(growthSummaryResponse.data);
      } else {
        message.error('获取增长汇总数据失败');
      }

      if (dataRangeResponse.data.success) {
        setCurrentDataRange(dataRangeResponse.data);
      } else {
        console.warn('获取数据范围失败');
      }
    } catch (error: any) {
      console.error('获取总览数据失败:', error);
      message.error('获取总览数据失败');
    } finally {
      setOverviewLoading(false);
    }
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    const parts = key.split('_');
    if (parts.length >= 3) {
      const platform = parts.slice(0, 2).join('_'); // wechat_mp
      const dataType = parts.slice(2).join('_'); // content_trend
      
      setSelectedPlatform(platform);
      setSelectedDataType(dataType);
      setPagination(prev => ({ ...prev, current: 1 }));
    }
  };

  const handleTableChange = (paginationInfo: any) => {
    setPagination(prev => ({
      ...prev,
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize
    }));
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleRefresh = () => {
    fetchDataList();
  };

  // 用户来源映射
  const userSourceMapping: { [key: string]: string } = {
    "0": "其他合计",
    "1": "公众号搜索",
    "17": "名片分享",
    "30": "扫描二维码",
    "57": "文章内账号名称",
    "100": "微信广告",
    "161": "他人转载",
    "149": "小程序关注",
    "200": "视频号",
    "201": "直播"
  };

  // 生成表格列配置
  const generateColumns = () => {
    const config = dataConfig[selectedDataType];
    if (!config) return [];

    return config.columns.map(col => ({
      title: col.title,
      dataIndex: col.key,
      key: col.key,
      render: (value: any) => {
        if (col.type === 'date') {
          return value ? new Date(value).toLocaleDateString() : '-';
        }
        if (col.type === 'datetime') {
          return value ? new Date(value).toLocaleString() : '-';
        }
        if (col.type === 'number') {
          return typeof value === 'number' ? value.toLocaleString() : value || 0;
        }
        if (col.type === 'url') {
          return value ? (
            <a href={value} target="_blank" rel="noopener noreferrer">
              查看链接
            </a>
          ) : '-';
        }
        if (col.type === 'user_source') {
          return userSourceMapping[String(value)] || `未知来源(${value})`;
        }
        return value || '-';
      },
      sorter: col.type === 'number' || col.type === 'date' || col.type === 'datetime',
      width: col.key === 'account_name' ? 150 :
             col.key === 'updated_at' ? 180 :
             col.type === 'text' ? 200 : 120,
      fixed: col.key === 'account_name' ? 'left' as const :
             col.key === 'updated_at' ? 'right' as const : undefined
    }));
  };

  const currentConfig = dataConfig[selectedDataType];

  return (
    <div style={{ height: '100vh' }}>
      <Layout style={{ height: '100%' }}>
        <Sider width={250} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
          <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
            <h3>数据明细</h3>
          </div>
          <Menu
            mode="inline"
            selectedKeys={[`${selectedPlatform}_${selectedDataType}`]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ height: 'calc(100% - 64px)', borderRight: 0 }}
          />
        </Sider>
        
        <Layout>
          <Content style={{ padding: '24px', background: '#fff' }}>
            {selectedDataType === 'overview' ? (
              // 总览视图
              <div>
                <h2 style={{ marginBottom: 24 }}>数据总览</h2>

                {/* 社媒账号数据汇总表 */}
                <Card
                  title={`社媒账号数据汇总${accountSummary?.data_date_range ? `（${accountSummary.data_date_range}）` : '（数据日期跨度）'}`}
                  style={{ marginBottom: 24 }}
                  loading={overviewLoading}
                >
                  {accountSummary && (
                    <Table
                      dataSource={accountSummary.data}
                      rowKey="account_name"
                      pagination={false}
                      scroll={{ x: 'max-content' }}
                      size="small"
                      columns={[
                        {
                          title: '账号名称',
                          dataIndex: 'account_name',
                          key: 'account_name',
                          fixed: 'left',
                          width: 150
                        },
                        {
                          title: '公众号关注数',
                          children: accountSummary.date_columns?.map((dateCol: string) => ({
                            title: dateCol,
                            dataIndex: dateCol,
                            key: dateCol,
                            width: 120,
                            render: (value: number) => value?.toLocaleString() || '-'
                          })) || []
                        }
                      ]}
                    />
                  )}
                </Card>

                {/* 关注数合计净增长表 */}
                <Card
                  title={`关注数合计净增长${growthSummary?.data_date_range ? `（${growthSummary.data_date_range}）` : '（数据日期跨度）'}`}
                  loading={overviewLoading}
                >
                  {growthSummary && (
                    <Table
                      dataSource={growthSummary.data}
                      rowKey="account_name"
                      pagination={false}
                      scroll={{ x: 'max-content' }}
                      size="small"
                      columns={[
                        {
                          title: '账号名称',
                          dataIndex: 'account_name',
                          key: 'account_name',
                          fixed: 'left',
                          width: 150
                        },
                        {
                          title: '新增粉丝',
                          dataIndex: 'new_user',
                          key: 'new_user',
                          width: 100,
                          render: (value: number) => value?.toLocaleString() || 0
                        },
                        {
                          title: '取消关注',
                          dataIndex: 'cancel_user',
                          key: 'cancel_user',
                          width: 100,
                          render: (value: number) => value?.toLocaleString() || 0
                        },
                        {
                          title: '累计关注粉丝',
                          dataIndex: 'cumulate_user',
                          key: 'cumulate_user',
                          width: 120,
                          render: (value: number) => value?.toLocaleString() || 0
                        },
                        ...(growthSummary.user_sources?.map((source: number) => ({
                          title: userSourceMapping[String(source)] || `全部来源`,
                          dataIndex: `source_${source}`,
                          key: `source_${source}`,
                          width: 100,
                          render: (value: number) => value?.toLocaleString() || 0
                        })) || [])
                      ]}
                    />
                  )}
                </Card>
              </div>
            ) : currentConfig ? (
              // 原有的数据明细视图
              <>
                <Card
                  title={currentConfig.name}
                  extra={
                    <Space>
                      <Select
                        value={selectedAccount}
                        onChange={setSelectedAccount}
                        style={{ width: 200 }}
                        placeholder="选择账号"
                      >
                        <Option key="all" value="all">
                          <Space>
                            全部账号
                            <Tag color="blue">
                              {accounts.length} 个账号
                            </Tag>
                          </Space>
                        </Option>
                        {accounts.map(account => (
                          <Option key={account.id} value={account.id}>
                            <Space>
                              {account.name}
                            </Space>
                          </Option>
                        ))}
                      </Select>
                      <Search
                        placeholder="搜索..."
                        allowClear
                        onSearch={handleSearch}
                        style={{ width: 200 }}
                      />
                      <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
                        刷新
                      </Button>
                    </Space>
                  }
                  style={{ marginBottom: 16 }}
                >
                  <p style={{ margin: 0, color: '#666' }}>{currentConfig.description}</p>
                </Card>

                <Table
                  columns={generateColumns()}
                  dataSource={dataList}
                  rowKey="id"
                  loading={loading}
                  pagination={{
                    ...pagination,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`
                  }}
                  onChange={handleTableChange}
                  scroll={{ x: 'max-content' }}
                  size="small"
                />
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin size="large" />
                <p style={{ marginTop: 16 }}>加载中...</p>
              </div>
            )}
          </Content>
        </Layout>
      </Layout>
    </div>
  );
};

export default DataDetails;
