import React, { useState, useEffect } from 'react';
import { 
  Card, DatePicker, Button, Progress, Alert, message, 
  Descriptions, Table, Space, Typography, Divider, Select, Checkbox,
  Row, Col, Tag
} from 'antd';
import { DownloadOutlined, ReloadOutlined, HistoryOutlined, CloudDownloadOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import api from '../services/api';

const { RangePicker } = DatePicker;
const { Title, Text } = Typography;
const { Option } = Select;

interface DownloadTask {
  id: number;
  start_date: string;
  end_date: string;
  selected_accounts: number[];
  selected_data_types: string[];
  status: string;
  total_files: number;
  completed_files: number;
  current_account_name?: string;
  current_step?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  progress_percent: number;
  duration?: string;
  can_download: boolean;
}

interface Account {
  id: number;
  name: string;
  platform: string;
}

interface DataTypeConfig {
  [key: string]: string;
}

const DataDownload: React.FC = () => {
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);
  const [selectedAccounts, setSelectedAccounts] = useState<number[]>([]);
  const [selectedDataTypes, setSelectedDataTypes] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentTask, setCurrentTask] = useState<DownloadTask | null>(null);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [dataTypes, setDataTypes] = useState<DataTypeConfig>({});
  const [history, setHistory] = useState<DownloadTask[]>([]);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // 获取可用账号
  const fetchAccounts = async () => {
    try {
      const response = await api.get('/data-download/accounts');
      if (response.data.success) {
        setAccounts(response.data.accounts);
      }
    } catch (error) {
      console.error('获取账号列表失败:', error);
    }
  };

  // 获取数据类型配置
  const fetchDataTypes = async () => {
    try {
      const response = await api.get('/data-download/data-types');
      if (response.data.success) {
        setDataTypes(response.data.data_types);
      }
    } catch (error) {
      console.error('获取数据类型失败:', error);
    }
  };

  // 获取下载历史
  const fetchHistory = async () => {
    try {
      const response = await api.get('/data-download/history');
      if (response.data.success) {
        setHistory(response.data.data);
      }
    } catch (error) {
      console.error('获取下载历史失败:', error);
    }
  };

  // 检查是否有正在运行的任务
  const checkRunningTask = async () => {
    try {
      const response = await api.get('/data-download/history?page_size=1');
      if (response.data.success && response.data.data.length > 0) {
        const latestTask = response.data.data[0];
        if (latestTask.status === 'running') {
          setCurrentTask(latestTask);
          startPolling(latestTask.id);
        }
      }
    } catch (error) {
      console.error('检查运行任务失败:', error);
    }
  };

  // 开始轮询任务状态
  const startPolling = (taskId: number) => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    const interval = setInterval(async () => {
      try {
        const response = await api.get(`/data-download/status/${taskId}`);
        if (response.data.success) {
          setCurrentTask(response.data);
          
          // 如果任务完成，停止轮询
          if (response.data.status === 'completed' || response.data.status === 'failed') {
            clearInterval(interval);
            setPollingInterval(null);
            fetchHistory(); // 刷新历史记录
            
            if (response.data.status === 'completed') {
              message.success('数据下载完成！');
            } else {
              message.error('数据下载失败！');
            }
          }
        }
      } catch (error) {
        console.error('获取任务状态失败:', error);
        clearInterval(interval);
        setPollingInterval(null);
      }
    }, 2000);

    setPollingInterval(interval);
  };

  // 启动下载任务
  const handleStartDownload = async () => {
    if (!dateRange || !dateRange[0] || !dateRange[1]) {
      message.error('请选择日期范围');
      return;
    }

    if (selectedAccounts.length === 0) {
      message.error('请至少选择一个账号');
      return;
    }

    if (selectedDataTypes.length === 0) {
      message.error('请至少选择一种数据类型');
      return;
    }

    const startDate = dateRange[0].format('YYYY-MM-DD');
    const endDate = dateRange[1].format('YYYY-MM-DD');

    setLoading(true);
    try {
      const response = await api.post('/data-download/start', {
        start_date: startDate,
        end_date: endDate,
        account_ids: selectedAccounts,
        data_types: selectedDataTypes
      });

      if (response.data.success) {
        message.success('数据下载任务已启动');
        setCurrentTask({
          id: response.data.task_id,
          start_date: startDate,
          end_date: endDate,
          selected_accounts: selectedAccounts,
          selected_data_types: selectedDataTypes,
          status: 'running',
          total_files: response.data.total_files,
          completed_files: 0,
          current_step: '准备中...',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          progress_percent: 0,
          can_download: false
        });
        
        startPolling(response.data.task_id);
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '启动下载失败');
    } finally {
      setLoading(false);
    }
  };

  // 下载文件
  const handleDownload = async (taskId: number) => {
    try {
      const response = await api.get(`/data-download/download/${taskId}`, {
        responseType: 'blob'
      });
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      
      // 从响应头获取文件名，或使用默认文件名
      const contentDisposition = response.headers['content-disposition'];
      let filename = `data_download_${taskId}.zip`;
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }
      
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      message.success('文件下载已开始');
    } catch (error: any) {
      message.error(error.response?.data?.detail || '下载失败');
    }
  };

  // 历史记录表格列定义
  const historyColumns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '日期范围',
      key: 'date_range',
      render: (record: DownloadTask) => `${record.start_date} 至 ${record.end_date}`
    },
    {
      title: '账号数',
      key: 'account_count',
      render: (record: DownloadTask) => record.selected_accounts.length
    },
    {
      title: '数据类型数',
      key: 'data_type_count',
      render: (record: DownloadTask) => record.selected_data_types.length
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          'running': { color: 'blue', text: '运行中' },
          'completed': { color: 'green', text: '已完成' },
          'failed': { color: 'red', text: '失败' }
        };
        const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '进度',
      dataIndex: 'progress_percent',
      key: 'progress_percent',
      render: (percent: number) => `${percent}%`
    },
    {
      title: '耗时',
      dataIndex: 'duration',
      key: 'duration'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (record: DownloadTask) => (
        <Space>
          {record.can_download && (
            <Button
              type="link"
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(record.id)}
            >
              下载
            </Button>
          )}
        </Space>
      )
    }
  ];

  useEffect(() => {
    fetchAccounts();
    fetchDataTypes();
    fetchHistory();
    checkRunningTask();

    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, []);

  const isTaskRunning = Boolean(currentTask && currentTask.status === 'running');

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>数据下载</Title>
      
      {/* 下载配置 */}
      <Card title="下载配置" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Text strong>选择日期范围（最长30天）：</Text>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              disabled={isTaskRunning}
              style={{ marginLeft: 16, width: 300 }}
              disabledDate={(current) => {
                // 禁用未来日期
                return current && current > dayjs().endOf('day');
              }}
            />
          </Col>
          
          <Col span={12}>
            <Text strong>选择账号：</Text>
            <Select
              mode="multiple"
              value={selectedAccounts}
              onChange={setSelectedAccounts}
              disabled={isTaskRunning}
              style={{ width: '100%', marginTop: 8 }}
              placeholder="请选择要下载的账号"
            >
              {accounts.map(account => (
                <Option key={account.id} value={account.id}>
                  {account.name} ({account.platform})
                </Option>
              ))}
            </Select>
          </Col>
          
          <Col span={12}>
            <Text strong>选择数据类型：</Text>
            <Checkbox.Group
              value={selectedDataTypes}
              onChange={setSelectedDataTypes}
              disabled={isTaskRunning}
              style={{ marginTop: 8 }}
            >
              <Row>
                {Object.entries(dataTypes).map(([key, name]) => (
                  <Col span={24} key={key} style={{ marginBottom: 8 }}>
                    <Checkbox value={key}>
                      {name}
                      {key === 'content_source' && (
                        <Text type="secondary" style={{ fontSize: '12px', marginLeft: 8 }}>
                          (自动生成饼图)
                        </Text>
                      )}
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Col>
        </Row>
        
        <Divider />
        
        <Button
          type="primary"
          icon={<CloudDownloadOutlined />}
          onClick={handleStartDownload}
          loading={loading}
          disabled={isTaskRunning}
          size="large"
        >
          开始下载
        </Button>

        {isTaskRunning && (
          <Alert
            message="注意"
            description="数据下载过程中会从微信公众号后台下载Excel文件，然后按照指定的目录结构打包成ZIP文件。请耐心等待。"
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </Card>

      {/* 当前任务进度 */}
      {currentTask && (
        <Card title="下载进度" style={{ marginBottom: 24 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Descriptions column={2}>
              <Descriptions.Item label="任务ID">{currentTask.id}</Descriptions.Item>
              <Descriptions.Item label="日期范围">
                {currentTask.start_date} 至 {currentTask.end_date}
              </Descriptions.Item>
              <Descriptions.Item label="总文件数">{currentTask.total_files}</Descriptions.Item>
              <Descriptions.Item label="已完成">{currentTask.completed_files}</Descriptions.Item>
              <Descriptions.Item label="当前账号">{currentTask.current_account_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="当前步骤">{currentTask.current_step || '-'}</Descriptions.Item>
            </Descriptions>

            <Progress
              percent={currentTask.progress_percent}
              status={currentTask.status === 'failed' ? 'exception' : 'active'}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />

            {currentTask.error_message && (
              <Alert
                message="错误信息"
                description={currentTask.error_message}
                type="error"
                showIcon
              />
            )}

            {currentTask.status === 'completed' && currentTask.can_download && (
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={() => handleDownload(currentTask.id)}
                size="large"
              >
                下载ZIP文件
              </Button>
            )}
          </Space>
        </Card>
      )}

      {/* 历史记录 */}
      <Card
        title={
          <Space>
            <HistoryOutlined />
            下载历史记录
          </Space>
        }
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={() => { fetchHistory(); }}
          >
            刷新
          </Button>
        }
      >
        <Table
          columns={historyColumns}
          dataSource={history}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          size="small"
        />
      </Card>
    </div>
  );
};

export default DataDownload;
