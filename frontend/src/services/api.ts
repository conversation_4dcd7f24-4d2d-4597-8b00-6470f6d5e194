import axios from 'axios';

// 根据环境变量确定API基础URL
const getBaseURL = () => {
  // // 在生产环境中使用环境变量或默认的生产API地址
  // if (process.env.NODE_ENV === 'production') {
  //   return process.env.REACT_APP_API_URL || 'https://api.sm.shishu.me';
  // }
  // // 开发环境使用本地地址
  return '/api';
};

const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 300000, // 5分钟超时，适合长时间操作
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;