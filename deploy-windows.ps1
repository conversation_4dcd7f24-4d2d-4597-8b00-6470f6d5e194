# 社交媒体管理系统 - Windows PowerShell 部署脚本
# 适用于Windows 11主机的Docker部署

param(
    [string]$Action = "deploy",
    [switch]$Force = $false
)

# 颜色函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = "Red"
        "Green" = "Green"
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Blue"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 检查Docker环境
function Test-DockerEnvironment {
    Write-Info "检查Docker环境..."
    
    # 检查Docker Desktop是否安装
    try {
        $dockerVersion = docker --version
        Write-Success "Docker已安装: $dockerVersion"
    }
    catch {
        Write-Error "Docker未安装或未启动，请先安装Docker Desktop"
        return $false
    }
    
    # 检查Docker Compose
    try {
        $composeVersion = docker-compose --version
        Write-Success "Docker Compose已安装: $composeVersion"
    }
    catch {
        Write-Error "Docker Compose未安装"
        return $false
    }
    
    # 检查Docker服务状态
    try {
        docker info | Out-Null
        Write-Success "Docker服务运行正常"
        return $true
    }
    catch {
        Write-Error "Docker服务未运行，请启动Docker Desktop"
        return $false
    }
}

# 配置防火墙规则
function Set-FirewallRules {
    if (-not (Test-Administrator)) {
        Write-Warning "需要管理员权限来配置防火墙规则"
        return
    }
    
    Write-Info "配置防火墙规则..."
    
    try {
        # 允许HTTP访问
        New-NetFirewallRule -DisplayName "Social Media Manager HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow -ErrorAction SilentlyContinue
        
        # 允许HTTPS访问
        New-NetFirewallRule -DisplayName "Social Media Manager HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow -ErrorAction SilentlyContinue
        
        Write-Success "防火墙规则配置完成"
    }
    catch {
        Write-Warning "防火墙规则配置失败: $($_.Exception.Message)"
    }
}

# 创建必要目录
function New-RequiredDirectories {
    Write-Info "创建必要目录..."
    
    $directories = @(
        "nginx\conf.d",
        "nginx\ssl",
        "nginx\logs",
        "data",
        "user_data",
        "logs",
        "backups",
        "scripts"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Info "创建目录: $dir"
        }
    }
    
    Write-Success "目录创建完成"
}

# 设置环境变量
function Set-EnvironmentVariables {
    Write-Info "设置环境变量..."
    
    if (-not (Test-Path ".env.lan")) {
        Write-Error ".env.lan文件不存在，请先创建环境变量文件"
        return $false
    }
    
    # 复制环境变量文件
    Copy-Item ".env.lan" ".env" -Force
    
    # 生成新的SECRET_KEY（如果需要）
    $envContent = Get-Content ".env" -Raw
    if ($envContent -match "your-super-secret-key-change-this-in-production") {
        Write-Warning "检测到默认密钥，正在生成新的安全密钥..."
        
        # 生成随机密钥
        $bytes = New-Object byte[] 64
        [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
        $newSecretKey = [Convert]::ToBase64String($bytes)
        
        $envContent = $envContent -replace "your-super-secret-key-change-this-in-production", $newSecretKey
        Set-Content ".env" $envContent
        
        Write-Success "新的安全密钥已生成"
    }
    
    Write-Success "环境变量设置完成"
    return $true
}

# 停止现有服务
function Stop-ExistingServices {
    Write-Info "停止现有服务..."
    
    try {
        docker-compose -f docker-compose.lan.yml down 2>$null
        Write-Success "现有服务已停止"
    }
    catch {
        Write-Info "没有运行中的服务"
    }
}

# 构建和启动服务
function Start-Services {
    Write-Info "构建和启动服务..."
    
    try {
        # 拉取最新镜像
        Write-Info "拉取基础镜像..."
        docker-compose -f docker-compose.lan.yml pull
        
        # 构建自定义镜像
        Write-Info "构建应用镜像..."
        docker-compose -f docker-compose.lan.yml build --no-cache
        
        # 启动服务
        Write-Info "启动服务..."
        docker-compose -f docker-compose.lan.yml up -d
        
        Write-Success "服务启动完成"
        return $true
    }
    catch {
        Write-Error "服务启动失败: $($_.Exception.Message)"
        return $false
    }
}

# 等待服务就绪
function Wait-ForServices {
    Write-Info "等待服务就绪..."
    
    $maxAttempts = 30
    $attempt = 1
    
    while ($attempt -le $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost/api/" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Success "后端服务已就绪"
                break
            }
        }
        catch {
            Write-Info "等待后端服务启动... ($attempt/$maxAttempts)"
            Start-Sleep -Seconds 5
            $attempt++
        }
    }
    
    if ($attempt -gt $maxAttempts) {
        Write-Error "后端服务启动超时"
        return $false
    }
    
    # 检查前端服务
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "前端服务已就绪"
        }
    }
    catch {
        Write-Warning "前端服务可能未完全就绪，请稍后检查"
    }
    
    return $true
}

# 显示部署信息
function Show-DeploymentInfo {
    Write-Success "部署完成！"
    Write-Host ""
    Write-Host "==========================================" -ForegroundColor Cyan
    Write-Host "  社交媒体管理系统 - 局域网部署信息" -ForegroundColor Cyan
    Write-Host "==========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🌐 访问地址：" -ForegroundColor Yellow
    Write-Host "  - 内网穿透: http://sm.dev.mynatapp.cc" -ForegroundColor White
    Write-Host "  - 本地访问: http://************" -ForegroundColor White
    Write-Host "  - 本地访问: http://localhost" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 管理命令：" -ForegroundColor Yellow
    Write-Host "  查看状态: docker-compose -f docker-compose.lan.yml ps" -ForegroundColor White
    Write-Host "  查看日志: docker-compose -f docker-compose.lan.yml logs -f" -ForegroundColor White
    Write-Host "  重启服务: docker-compose -f docker-compose.lan.yml restart" -ForegroundColor White
    Write-Host "  停止服务: docker-compose -f docker-compose.lan.yml down" -ForegroundColor White
    Write-Host ""
    Write-Host "📁 重要目录：" -ForegroundColor Yellow
    Write-Host "  用户数据: .\user_data" -ForegroundColor White
    Write-Host "  应用数据: .\data" -ForegroundColor White
    Write-Host "  日志文件: .\logs" -ForegroundColor White
    Write-Host "  Nginx日志: .\nginx\logs" -ForegroundColor White
    Write-Host ""
    Write-Host "🔒 安全提醒：" -ForegroundColor Yellow
    Write-Host "  - 请确保防火墙已正确配置" -ForegroundColor White
    Write-Host "  - 建议定期备份数据" -ForegroundColor White
    Write-Host "  - 生产环境请启用HTTPS" -ForegroundColor White
    Write-Host ""
    Write-Host "==========================================" -ForegroundColor Cyan
}

# 主函数
function Main {
    Write-Host "==========================================" -ForegroundColor Cyan
    Write-Host "  社交媒体管理系统 - Windows 部署脚本" -ForegroundColor Cyan
    Write-Host "==========================================" -ForegroundColor Cyan
    Write-Host ""
    
    switch ($Action.ToLower()) {
        "deploy" {
            if (-not (Test-DockerEnvironment)) { exit 1 }
            Set-FirewallRules
            New-RequiredDirectories
            if (-not (Set-EnvironmentVariables)) { exit 1 }
            Stop-ExistingServices
            if (-not (Start-Services)) { exit 1 }
            if (-not (Wait-ForServices)) { exit 1 }
            Show-DeploymentInfo
        }
        "start" {
            docker-compose -f docker-compose.lan.yml up -d
            Write-Success "服务已启动"
        }
        "stop" {
            docker-compose -f docker-compose.lan.yml down
            Write-Success "服务已停止"
        }
        "restart" {
            docker-compose -f docker-compose.lan.yml restart
            Write-Success "服务已重启"
        }
        "status" {
            docker-compose -f docker-compose.lan.yml ps
        }
        "logs" {
            docker-compose -f docker-compose.lan.yml logs -f
        }
        default {
            Write-Host "用法: .\deploy-windows.ps1 [-Action <deploy|start|stop|restart|status|logs>] [-Force]"
            Write-Host ""
            Write-Host "参数:"
            Write-Host "  -Action deploy   完整部署 (默认)"
            Write-Host "  -Action start    启动服务"
            Write-Host "  -Action stop     停止服务"
            Write-Host "  -Action restart  重启服务"
            Write-Host "  -Action status   查看状态"
            Write-Host "  -Action logs     查看日志"
            Write-Host "  -Force           强制执行"
        }
    }
}

# 执行主函数
Main
